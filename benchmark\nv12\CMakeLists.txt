

# 分别设置ffmpeg的链接库和头文件
set(out ${CMAKE_SOURCE_DIR}/benchmark/third_party)

set(FFMPEG_LIBS ${out}/ffmpeg-lib/lib/libavcodec.a 
               ${out}/ffmpeg-lib/lib/libavfilter.a
               ${out}/ffmpeg-lib/lib/libavformat.a
                ${out}/ffmpeg-lib/lib/libavutil.a
                ${out}/ffmpeg-lib/lib/libavresample.a
                ${out}/ffmpeg-lib/lib/libswresample.a
                ${out}/ffmpeg-lib/lib/libswscale.a
                ${out}/ffmpeg-lib/lib/libavdevice.a
                ${out}/ffmpeg-lib/lib/libpostproc.a )
include_directories(${out}/ffmpeg-lib/include)


add_executable(ffmpeg_crop ffmpeg_crop.cpp)
target_link_libraries(ffmpeg_crop ${OpenCV_LIBS} -Wl,--whole-archive ${FFMPEG_LIBS} -Wl,--no-whole-archive)

add_executable(ffmpeg_resize ffmpeg_resize.cpp)
target_link_libraries(ffmpeg_resize ${OpenCV_LIBS} -Wl,--whole-archive ${FFMPEG_LIBS} -Wl,--no-whole-archive)

add_executable(libyuv_crop libyuv_crop.cpp)
target_link_libraries(libyuv_crop ${OpenCV_LIBS} -Wl,--whole-archive ${FFMPEG_LIBS} -Wl,--no-whole-archive)
target_link_libraries(libyuv_crop ${LIBYUV_LIB})

add_executable(libyuv_resize libyuv_resize.cpp)
target_link_libraries(libyuv_resize ${OpenCV_LIBS} -Wl,--whole-archive ${FFMPEG_LIBS} -Wl,--no-whole-archive)
target_link_libraries(libyuv_resize ${LIBYUV_LIB})