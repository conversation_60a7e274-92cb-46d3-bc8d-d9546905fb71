
#include <stdio.h>
#include <iostream>
#include <fstream>
#include <cstdlib>
#include <malloc.h>
#include <iostream>
#include <string>
#include <numeric>

#include <opencv2/core/core.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc/types_c.h>

extern "C"
{
    #include "libavfilter/avfilter.h"
    #include "libswscale/swscale.h"
    #include "libavcodec/avcodec.h"  
    #include "libavutil/imgutils.h"
    #include "libavutil/opt.h"
    #include "libavutil/frame.h"
    #include "libavutil/pixfmt.h"
    #include "libavutil/imgutils.h"
    #include "libavcodec/avcodec.h"
    #include "libavutil/opt.h"
    #include "libavfilter/buffersrc.h"
    #include "libavfilter/buffersink.h"
    #include "libavformat/avformat.h"  
};


int main() {
     
        std::vector<float> time6;
        
        cv::Mat Img = cv::imread("1.jpg");
	
	if (Img.empty())
	{
		std::cout << "empty!check your image";
		return 0;
	}
	int in_width = Img.cols;
	int in_height = Img.rows;
        for(int i=0;i<100;i++)
    {
        
        //jpg图片转nv12
        struct SwsContext* img_convert_ctx;
        img_convert_ctx = sws_getContext(in_width, in_height,AV_PIX_FMT_BGR24 ,in_width, in_height, AV_PIX_FMT_NV12, SWS_POINT,NULL, NULL, NULL);
        
        uint8_t *  Plane_bgr[4];
        Plane_bgr[0]=Img.data;
        Plane_bgr[1]=NULL;
        Plane_bgr[2]=NULL;
        Plane_bgr[3]=NULL;
	int LineSize_bgr[4];
        LineSize_bgr[0]=in_width*3;
        LineSize_bgr[1]=0;
        LineSize_bgr[2]=0;
        LineSize_bgr[3]=0;
        uint8_t *  Plane_nv12[4];
        Plane_nv12[0] = (uint8_t*)malloc(in_width * in_height * 1.5);
	Plane_nv12[1] = Plane_nv12[0]+in_width * in_height;
	Plane_nv12[2] = NULL;
	Plane_nv12[3] = NULL;
        int LineSize_nv12[4];
        LineSize_nv12[0]=in_width;
        LineSize_nv12[1]=in_width;
        LineSize_nv12[2]=0;
        LineSize_nv12[3]=0;
        
        sws_scale(img_convert_ctx, Plane_bgr, LineSize_bgr,0, in_height, reinterpret_cast<uint8_t *const *>(&Plane_nv12), reinterpret_cast<const int *>(&LineSize_nv12));
        
        //nv12 crop
        int offset_x = 200;
	int offset_y =200;
	int c_wid = 640;
	int c_hei = 480;

        double s = (double)cv::getTickCount();
	AVFilterGraph* filterGraph = avfilter_graph_alloc();
	char args[512] = "";
	snprintf(args, sizeof(args),
		"buffer=video_size=%dx%d:pix_fmt=%d:time_base=1/1:pixel_aspect=1/1[in];" // Parsed_buffer_0
		"[in]crop=x=%d:y=%d:out_w=%d:out_h=%d[out];"                             // Parsed_crop_1
		"[out]buffersink",                                                       // Parsed_buffersink_2
		1920, 1080, AV_PIX_FMT_NV12,
		offset_x, offset_y, c_wid, c_hei);
	AVFilterInOut* inputs = NULL;
	AVFilterInOut* outputs = NULL;
	avfilter_graph_parse2(filterGraph, args, &inputs, &outputs);
	avfilter_graph_config(filterGraph, NULL);
	AVFilterContext* srcFilterCtx = avfilter_graph_get_filter(filterGraph, "Parsed_buffer_0");
	AVFilterContext* sinkFilterCtx = avfilter_graph_get_filter(filterGraph, "Parsed_buffersink_2");
	AVFrame* srcFrame = av_frame_alloc();
	srcFrame->width = 1920;
	srcFrame->height = 1080;
	srcFrame->format = AV_PIX_FMT_NV12;
	// 填充AVFrame
	av_image_fill_arrays(srcFrame->data, srcFrame->linesize, Plane_nv12[0], AV_PIX_FMT_NV12, 1920, 1080, 1);
      
	av_buffersrc_add_frame(srcFilterCtx, srcFrame);
	av_buffersink_get_frame(sinkFilterCtx, srcFrame);
        double j = (double)cv::getTickCount();

        double t6=(j-s)/cv::getTickFrequency();
        time6.push_back(t6*1000);
	
	// 获取crop完成后的数据
	uint8_t* resize_nv12[4];
	resize_nv12[0]= srcFrame->data[0];
	resize_nv12[1] = srcFrame->data[1];
	resize_nv12[2] = NULL;
	resize_nv12[3] = NULL;

	int resize_line_nv12[4];
	resize_line_nv12[0] = 1920;
	resize_line_nv12[1] = 1920;
	resize_line_nv12[2] = 0;
	resize_line_nv12[3] = 0;

	//nv12转bgr,用于验证转换后数据是否正确
        
	uint8_t* new_Plane_bgr[4];
	new_Plane_bgr[0] = (uint8_t*)malloc(c_wid * c_hei * 3);
	new_Plane_bgr[1] = NULL;
	new_Plane_bgr[2] = NULL;
	new_Plane_bgr[3] = NULL;
	int new_LineSize_bgr[4];
	new_LineSize_bgr[0] = c_wid * 3;
	new_LineSize_bgr[1] = 0;
	new_LineSize_bgr[2] = 0;
	new_LineSize_bgr[3] = 0;
	img_convert_ctx = sws_getContext(c_wid, c_hei, AV_PIX_FMT_NV12, c_wid, c_hei, AV_PIX_FMT_BGR24, SWS_POINT, NULL, NULL, NULL);
	sws_scale(img_convert_ctx, resize_nv12, resize_line_nv12, 0, c_hei,
		reinterpret_cast<uint8_t* const*>(&new_Plane_bgr), reinterpret_cast<const int*>(&new_LineSize_bgr));

	cv::Mat test = cv::Mat(c_hei, c_wid, CV_8UC3);
	test.data = new_Plane_bgr[0];
	cv::imwrite("haha.jpg", test);
	
	sws_freeContext(img_convert_ctx);
	avfilter_graph_free(&filterGraph);
	av_frame_free(&srcFrame);

    }
  
    std::cout<<"crop 循环次数："<<time6.size()<<std::endl;

    double tt6=std::accumulate(std::begin(time6),std::end(time6),0.0)/time6.size();
    std::cout<<"平均耗时："<<tt6<<std::endl;

    return 0;
    }
