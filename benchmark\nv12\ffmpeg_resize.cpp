
#include <stdio.h>
#include <iostream>
#include <fstream>
#include <cstdlib>
#include <malloc.h>
#include <iostream>
#include <string>
#include <numeric>

#include <opencv2/core/core.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc/types_c.h>

extern "C"
{
    #include "libavfilter/avfilter.h"
    #include "libswscale/swscale.h"
    #include "libavcodec/avcodec.h"  
    #include "libavutil/imgutils.h"
    #include "libavutil/opt.h"
    #include "libavutil/frame.h"
    #include "libavutil/pixfmt.h"
    #include "libavutil/imgutils.h"
    #include "libavcodec/avcodec.h"
    #include "libavutil/opt.h"
    #include "libavfilter/buffersrc.h"
    #include "libavfilter/buffersink.h"
    #include "libavformat/avformat.h"  
};

int main() {
        
  
        std::vector<float> time6;
        
        cv::Mat Img = cv::imread("1.jpg");
	
	if (Img.empty())
	{
		std::cout << "empty!check your image";
		return 0;
	}
	int in_width = Img.cols;
	int in_height = Img.rows;
        for(int i=0;i<100;i++)
    {
        
        //jpg图片转nv12
        struct SwsContext* img_convert_ctx;
        img_convert_ctx = sws_getContext(in_width, in_height,AV_PIX_FMT_BGR24 ,in_width, in_height, AV_PIX_FMT_NV12, SWS_POINT,NULL, NULL, NULL);
        
        uint8_t *  Plane_bgr[4];
        Plane_bgr[0]=Img.data;
        Plane_bgr[1]=NULL;
        Plane_bgr[2]=NULL;
        Plane_bgr[3]=NULL;
	int LineSize_bgr[4];
        LineSize_bgr[0]=in_width*3;
        LineSize_bgr[1]=0;
        LineSize_bgr[2]=0;
        LineSize_bgr[3]=0;
        uint8_t *  Plane_nv12[4];
        Plane_nv12[0] = (uint8_t*)malloc(in_width * in_height * 1.5);
	Plane_nv12[1] = Plane_nv12[0]+in_width * in_height;
	Plane_nv12[2] = NULL;
	Plane_nv12[3] = NULL;
        int LineSize_nv12[4];
        LineSize_nv12[0]=in_width;
        LineSize_nv12[1]=in_width;
        LineSize_nv12[2]=0;
        LineSize_nv12[3]=0;
        
        sws_scale(img_convert_ctx, Plane_bgr, LineSize_bgr,0, in_height, reinterpret_cast<uint8_t *const *>(&Plane_nv12), reinterpret_cast<const int *>(&LineSize_nv12));
        
        double s = (double)cv::getTickCount();

        //nv12->resize->bgr
	uint8_t* new_Plane_bgr[4];
	new_Plane_bgr[0] = (uint8_t*)malloc(640 * 480 * 3);
	new_Plane_bgr[1] = NULL;
	new_Plane_bgr[2] = NULL;
	new_Plane_bgr[3] = NULL;
	int new_LineSize_bgr[4];
	new_LineSize_bgr[0] = 480*3;
	new_LineSize_bgr[1] = 0;
	new_LineSize_bgr[2] = 0;
	new_LineSize_bgr[3] = 0;
	img_convert_ctx = sws_getContext(in_width, in_height, AV_PIX_FMT_NV12, 640, 480, AV_PIX_FMT_BGR24, SWS_POINT, NULL, NULL, NULL);
	sws_scale(img_convert_ctx, Plane_nv12, LineSize_nv12, 0, in_height,reinterpret_cast<uint8_t* const*>(&new_Plane_bgr), reinterpret_cast<const int*>(&new_LineSize_bgr));
      

        //nv12 resize
	// uint8_t *  new_Plane_nv12[4];
        // new_Plane_nv12[0] = (uint8_t*)malloc(640 * 480 * 1.5);
	// new_Plane_nv12[1] = new_Plane_nv12[0]+640 * 480;
	// new_Plane_nv12[2] = NULL;
	// new_Plane_nv12[3] = NULL;
        // int new_LineSize_nv12[4];
        // new_LineSize_nv12[0]=640;
        // new_LineSize_nv12[1]=640;
        // new_LineSize_nv12[2]=0;
        // new_LineSize_nv12[3]=0;

	// img_convert_ctx = sws_getContext(in_width, in_height, AV_PIX_FMT_NV12, 640, 480, AV_PIX_FMT_NV12, SWS_POINT, NULL, NULL, NULL);
        // double s = (double)cv::getTickCount();
	// sws_scale(img_convert_ctx, Plane_nv12, LineSize_nv12, 0, in_height,reinterpret_cast<uint8_t* const*>(&new_Plane_nv12), reinterpret_cast<const int*>(&new_LineSize_nv12));

        double j = (double)cv::getTickCount();
        double t6=(j-s)/cv::getTickFrequency();

        time6.push_back(t6*1000);
	
	// //nv12转bgr
	// uint8_t* new_Plane_bgr[4];
	// new_Plane_bgr[0] = (uint8_t*)malloc(c_wid * c_hei * 3);
	// new_Plane_bgr[1] = NULL;
	// new_Plane_bgr[2] = NULL;
	// new_Plane_bgr[3] = NULL;
	// int new_LineSize_bgr[4];
	// new_LineSize_bgr[0] = c_wid * 3;
	// new_LineSize_bgr[1] = 0;
	// new_LineSize_bgr[2] = 0;
	// new_LineSize_bgr[3] = 0;
	// img_convert_ctx = sws_getContext(c_wid, c_hei, AV_PIX_FMT_NV12, c_wid, c_hei, AV_PIX_FMT_BGR24, SWS_POINT, NULL, NULL, NULL);
	// sws_scale(img_convert_ctx, resize_nv12, resize_line_nv12, 0, c_hei,
	// 	reinterpret_cast<uint8_t* const*>(&new_Plane_bgr), reinterpret_cast<const int*>(&new_LineSize_bgr));

	// cv::Mat test = cv::Mat(c_hei, c_wid, CV_8UC3);
	// test.data = new_Plane_bgr[0];
	// cv::imwrite("haha.jpg", test);

	sws_freeContext(img_convert_ctx);
	

    }
    std::cout<<in_width<<in_height<<std::endl;
    std::cout<<"nv12->resize->brg 循环次数："<<time6.size()<<std::endl;

    double tt6=std::accumulate(std::begin(time6),std::end(time6),0.0)/time6.size();
    std::cout<<"平均耗时："<<tt6<<std::endl;

    return 0;
    }
