
#include <stdio.h>
#include <iostream>
#include <fstream>
#include <cstdlib>
#include <malloc.h>
#include <iostream>
#include <string>
#include <numeric>

#include <opencv2/core/core.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc/types_c.h>

extern "C"
{
    #include "libavfilter/avfilter.h"
    #include "libswscale/swscale.h"
    #include "libavcodec/avcodec.h"  
    #include "libavutil/imgutils.h"
    #include "libavutil/opt.h"
    #include "libavutil/frame.h"
    #include "libavutil/pixfmt.h"
    #include "libavutil/imgutils.h"
    #include "libavcodec/avcodec.h"
    #include "libavutil/opt.h"
    #include "libavfilter/buffersrc.h"
    #include "libavfilter/buffersink.h"
    #include "libavformat/avformat.h"  
};

#include "libyuv.h"

#define SUBSAMPLE(v, a, s) (v < 0) ? (-((-v + a) >> s)) : ((v + a) >> s)

int main() {
        
        std::vector<float> time1;
      
        cv::Mat Img = cv::imread("1.jpg");
	
	if (Img.empty())
	{
		std::cout << "empty!check your image";
		return 0;
	}
	int in_width = Img.cols;
	int in_height = Img.rows;
        for(int i=0;i<100;i++)
    {
        
        //jpg图片转nv12
        struct SwsContext* img_convert_ctx;
        img_convert_ctx = sws_getContext(in_width, in_height,AV_PIX_FMT_BGR24 ,in_width, in_height, AV_PIX_FMT_NV12, SWS_POINT,NULL, NULL, NULL);
        
        uint8_t *  Plane_bgr[4];
        Plane_bgr[0]=Img.data;
        Plane_bgr[1]=NULL;
        Plane_bgr[2]=NULL;
        Plane_bgr[3]=NULL;
	int LineSize_bgr[4];
        LineSize_bgr[0]=in_width*3;
        LineSize_bgr[1]=0;
        LineSize_bgr[2]=0;
        LineSize_bgr[3]=0;
        uint8_t *  Plane_nv12[4];
        Plane_nv12[0] = (uint8_t*)malloc(in_width * in_height * 1.5);
	Plane_nv12[1] = Plane_nv12[0]+in_width * in_height;
	Plane_nv12[2] = NULL;
	Plane_nv12[3] = NULL;
        int LineSize_nv12[4];
        LineSize_nv12[0]=in_width;
        LineSize_nv12[1]=in_width;
        LineSize_nv12[2]=0;
        LineSize_nv12[3]=0;
        
        sws_scale(img_convert_ctx, Plane_bgr, LineSize_bgr,0, in_height, reinterpret_cast<uint8_t *const *>(&Plane_nv12), reinterpret_cast<const int *>(&LineSize_nv12));
        

        //libyuv crop
        int offset_x=10;
        int offset_y=20;
        int crop_w=640;
        int crop_h=480;

        uint8_t * crop_i420[4];
        crop_i420[0] = (uint8_t*)malloc(crop_w * crop_h * 1.5);
	crop_i420[1] = crop_i420[0]+crop_w * crop_h;
	crop_i420[2] = crop_i420[1]+crop_w * crop_h /4;
	crop_i420[3] = NULL;
        int crop_lineSize[4];
        crop_lineSize[0]=crop_w;
        crop_lineSize[1]=crop_w/2;
        crop_lineSize[2]=crop_w/2;
        crop_lineSize[3]=0;

        double f = (double)cv::getTickCount();
        //crop
        ConvertToI420(Plane_nv12[0],in_width * in_height*1.5,
                crop_i420[0],crop_lineSize[0],
                crop_i420[1],crop_lineSize[1],
                crop_i420[2],crop_lineSize[2],
                offset_x,offset_y,
                in_width,in_height,
                crop_w,crop_h,
                libyuv::kRotate0, libyuv::FOURCC_NV12);

        //i420转nv12
        uint8_t *  crop_nv12[4];
        crop_nv12[0] = (uint8_t*)malloc(crop_w * crop_h * 1.5);
	crop_nv12[1] = crop_nv12[0]+crop_w * crop_h;
	crop_nv12[2] = NULL;
	crop_nv12[3] = NULL;
        int nv12_lineSize[4];
        nv12_lineSize[0]=crop_w;
        nv12_lineSize[1]=crop_w;
        nv12_lineSize[2]=0;
        nv12_lineSize[3]=0;
        libyuv::I420ToNV12(
                                (const uint8_t*)crop_i420[0], crop_lineSize[0],
                                (const uint8_t*)crop_i420[1], crop_lineSize[1],
                                (const uint8_t*)crop_i420[2], crop_lineSize[1],
                                (uint8_t*)crop_nv12[0], nv12_lineSize[0],
                                (uint8_t*)crop_nv12[1], nv12_lineSize[1],
                                crop_w, crop_h);

        double g = (double)cv::getTickCount();

        double t1=(g-f)/cv::getTickFrequency();
        time1.push_back(t1*1000);

	// //nv12转bgr
	// uint8_t* new_Plane_bgr[4];
	// new_Plane_bgr[0] = (uint8_t*)malloc(crop_w * crop_h * 3);
	// new_Plane_bgr[1] = NULL;
	// new_Plane_bgr[2] = NULL;
	// new_Plane_bgr[3] = NULL;
	// int new_LineSize_bgr[4];
	// new_LineSize_bgr[0] = crop_w * 3;
	// new_LineSize_bgr[1] = 0;
	// new_LineSize_bgr[2] = 0;
	// new_LineSize_bgr[3] = 0;
	// img_convert_ctx = sws_getContext(crop_w, crop_h, AV_PIX_FMT_NV12, crop_w, crop_h, AV_PIX_FMT_BGR24, SWS_POINT, NULL, NULL, NULL);
	// sws_scale(img_convert_ctx, crop_nv12, nv12_lineSize, 0, crop_h,
	// 	reinterpret_cast<uint8_t* const*>(&new_Plane_bgr), reinterpret_cast<const int*>(&new_LineSize_bgr));

	// cv::Mat test = cv::Mat(crop_h, crop_w, CV_8UC3);
	// test.data = new_Plane_bgr[0];
	// cv::imwrite("crop.jpg", test);

	sws_freeContext(img_convert_ctx);

    }
    std::cout<<"nv12->resize 循环次数："<<time1.size()<<std::endl;
    double tt1=std::accumulate(std::begin(time1),std::end(time1),0.0)/time1.size();
    std::cout<<"平均时间："<<tt1<<std::endl;
    return 0;
    }
