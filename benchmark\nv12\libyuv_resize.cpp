#include <stdio.h>
#include <iostream>
#include <fstream>
#include <cstdlib>
#include <malloc.h>
#include <iostream>
#include <string>
#include <numeric>

#include <opencv2/core/core.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc/types_c.h>

extern "C"
{
    #include "libavfilter/avfilter.h"
    #include "libswscale/swscale.h"
    #include "libavcodec/avcodec.h"  
    #include "libavutil/imgutils.h"
    #include "libavutil/opt.h"
    #include "libavutil/frame.h"
    #include "libavutil/pixfmt.h"
    #include "libavutil/imgutils.h"
    #include "libavcodec/avcodec.h"
    #include "libavutil/opt.h"
    #include "libavfilter/buffersrc.h"
    #include "libavfilter/buffersink.h"
    #include "libavformat/avformat.h"  
};

#include "libyuv.h"

#define SUBSAMPLE(v, a, s) (v < 0) ? (-((-v + a) >> s)) : ((v + a) >> s)

int main() {

        std::vector<float> time1;
        cv::Mat Img = cv::imread("1.jpg");
	
	if (Img.empty())
	{
		std::cout << "empty!check your image";
		return 0;
	}
	int in_width = Img.cols;
	int in_height = Img.rows;
        for(int i=0;i<100;i++)
    {
        
        //jpg图片转nv12
        struct SwsContext* img_convert_ctx;
        img_convert_ctx = sws_getContext(in_width, in_height,AV_PIX_FMT_BGR24 ,in_width, in_height, AV_PIX_FMT_NV12, SWS_POINT,NULL, NULL, NULL);
        
        uint8_t *  Plane_bgr[4];
        Plane_bgr[0]=Img.data;
        Plane_bgr[1]=NULL;
        Plane_bgr[2]=NULL;
        Plane_bgr[3]=NULL;
	int LineSize_bgr[4];
        LineSize_bgr[0]=in_width*3;
        LineSize_bgr[1]=0;
        LineSize_bgr[2]=0;
        LineSize_bgr[3]=0;
        uint8_t *  Plane_nv12[4];
        Plane_nv12[0] = (uint8_t*)malloc(in_width * in_height * 1.5);
	Plane_nv12[1] = Plane_nv12[0]+in_width * in_height;
	Plane_nv12[2] = NULL;
	Plane_nv12[3] = NULL;
        int LineSize_nv12[4];
        LineSize_nv12[0]=in_width;
        LineSize_nv12[1]=in_width;
        LineSize_nv12[2]=0;
        LineSize_nv12[3]=0;
        
        sws_scale(img_convert_ctx, Plane_bgr, LineSize_bgr,0, in_height, reinterpret_cast<uint8_t *const *>(&Plane_nv12), reinterpret_cast<const int *>(&LineSize_nv12));
        
        //nv12 分离uv平面使用数据
	uint32_t src_halfheight = SUBSAMPLE(in_height, 1, 1);
        uint32_t src_halfwidth = SUBSAMPLE(in_width, 1, 1);
        
        //resize后的尺寸
        int re_wid=640;
        int re_hei=480;

        uint8_t* resize_nv12[4];
	resize_nv12[0]= (uint8_t*)malloc(re_wid * re_hei * 3/2);
	resize_nv12[1] =resize_nv12[0]+re_wid * re_hei;
	resize_nv12[2] = NULL;
	resize_nv12[3] = NULL;

	int resize_line_nv12[4];
	resize_line_nv12[0] = re_wid;
	resize_line_nv12[1] = re_wid;
	resize_line_nv12[2] = 0;
	resize_line_nv12[3] = 0;

        double f = (double)cv::getTickCount();
        
        //resize y_plane
        libyuv::ScalePlane(Plane_nv12[0], LineSize_nv12[0], in_width, in_height, 
                                resize_nv12[0], resize_line_nv12[0],re_wid,re_hei,
                                libyuv::FilterMode::kFilterBilinear);
        // Split VUplane
        uint8_t* uv_data = new uint8_t[in_width * in_height / 2];
        uint8_t* v_data = uv_data;
        uint8_t* u_data = uv_data + in_height * in_width / 4;
        //malloc memory to store temp u v 
        uint8_t* temp_uv_data = new uint8_t[in_width * in_height / 2];
        uint8_t* temp_v_data = temp_uv_data;
        uint8_t* temp_u_data = temp_uv_data + in_width * in_height / 4;

        
        libyuv::SplitUVPlane(Plane_nv12[1], LineSize_nv12[1],
                        v_data, LineSize_nv12[1] >> 1,
                        u_data, LineSize_nv12[1] >> 1,
                        src_halfwidth, src_halfheight);
        // resize u and v
        libyuv::ScalePlane(u_data,LineSize_nv12[1] >> 1,
                        in_width >> 1, src_halfheight,
                        temp_u_data, resize_line_nv12[1] >> 1,
                        re_wid >> 1, re_hei >> 1,
                        libyuv::FilterMode::kFilterBilinear);

        libyuv::ScalePlane(v_data,LineSize_nv12[1] >> 1,
                        in_width >> 1, src_halfheight,
                        temp_v_data, resize_line_nv12[1] >> 1,
                        re_wid >> 1, re_hei >> 1,
                        libyuv::FilterMode::kFilterBilinear);
                        
        libyuv::MergeUVPlane(temp_v_data, resize_line_nv12[1] >> 1,
                        temp_u_data, resize_line_nv12[1] >> 1, 
                        resize_nv12[1], resize_line_nv12[1],
                        re_wid >> 1, re_hei >> 1);

        double g = (double)cv::getTickCount();

        double t1=(g-f)/cv::getTickFrequency();
        time1.push_back(t1*1000);
          
	//nv12转bgr
	uint8_t* new_Plane_bgr[4];
	new_Plane_bgr[0] = (uint8_t*)malloc(re_wid * re_hei * 3);
	new_Plane_bgr[1] = NULL;
	new_Plane_bgr[2] = NULL;
	new_Plane_bgr[3] = NULL;
	int new_LineSize_bgr[4];
	new_LineSize_bgr[0] = re_wid * 3;
	new_LineSize_bgr[1] = 0;
	new_LineSize_bgr[2] = 0;
	new_LineSize_bgr[3] = 0;
	img_convert_ctx = sws_getContext(re_wid, re_hei, AV_PIX_FMT_NV12, re_wid, re_hei, AV_PIX_FMT_BGR24, SWS_POINT, NULL, NULL, NULL);
	sws_scale(img_convert_ctx, resize_nv12, resize_line_nv12, 0, re_hei,
		reinterpret_cast<uint8_t* const*>(&new_Plane_bgr), reinterpret_cast<const int*>(&new_LineSize_bgr));

	cv::Mat test = cv::Mat(re_hei, re_wid, CV_8UC3);
	test.data = new_Plane_bgr[0];
	cv::imwrite("resize.jpg", test);

	delete[] uv_data;
        u_data = v_data = uv_data = nullptr;
        delete[] temp_uv_data;
        temp_u_data = temp_v_data = temp_uv_data = nullptr;   
	sws_freeContext(img_convert_ctx);
        free(resize_nv12[0]);

    }
    
    std::cout<<"双线性"<<std::endl;
    std::cout<<"nv12->resize 循环次数："<<time1.size()<<std::endl;
    double tt1=std::accumulate(std::begin(time1),std::end(time1),0.0)/time1.size();
    std::cout<<"平均时间："<<tt1<<std::endl;

    return 0;
    }
