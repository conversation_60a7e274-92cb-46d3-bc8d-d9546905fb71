add_executable(test_yuv2rgb_opencv test_yuv2rgb_opencv.cpp)
target_link_libraries(test_yuv2rgb_opencv  ${OpenCV_LIBS})

add_executable(test_yuv2rgb_ncnn test_yuv2rgb_ncnn.cpp)
target_link_libraries(test_yuv2rgb_ncnn  ${OpenCV_LIBS} ${ncnn_lib})

add_executable(test_yuv_resize test_yuv_resize.cpp)
target_link_libraries(test_yuv_resize  ${OpenCV_LIBS})

add_executable(test_yuv_crop_resize test_yuv_crop_resize.cpp)
target_link_libraries(test_yuv_crop_resize  ${OpenCV_LIBS})

if("${Dst_Platform}" STREQUAL android)
    add_executable(test_yuv_resize_paddlelite test_yuv_resize_paddlelite.cpp)
    target_link_libraries(test_yuv_resize_paddlelite  ${OpenCV_LIBS})
endif()

