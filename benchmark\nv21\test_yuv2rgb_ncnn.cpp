#include<iostream>
#include <vector>
#include <fstream>
#include <string>
#include <opencv2/opencv.hpp>
#include "ncnn/net.h"
#include <linux/videodev2.h>
using namespace std;
using namespace cv;

void bgr2nv21(cv::Mat bgr, cv::Mat& nv21)
{
    cv::Mat yuv;
    cv::cvtColor(bgr, yuv, cv::COLOR_BGR2YUV_YV12);
   
    nv21 = cv::Mat::zeros(yuv.rows, yuv.cols, CV_8UC1);
    memcpy(nv21.data, yuv.data, bgr.rows * bgr.cols);
    unsigned char* pnv21v = (unsigned char*)nv21.data + bgr.rows * bgr.cols;
    unsigned char* pnv21u = (unsigned char*)nv21.data + bgr.rows * bgr.cols + 1;
    unsigned char* pyuv_v = (unsigned char*)yuv.data + bgr.rows * bgr.cols;
    unsigned char* pyuv_u = (unsigned char*)yuv.data + bgr.rows * bgr.cols + bgr.rows * bgr.cols / 4;
    for (size_t i = 0; i < bgr.rows * bgr.cols / 4; i++)
    {
        *pnv21v = *pyuv_v;
        *pnv21u = *pyuv_u;

        pyuv_v++;
        pyuv_u++;
        pnv21v += 2;
        pnv21u += 2;
    }
}

int main()
{
    float total_time = 0;
    for (int i = 0; i < 50; i++)
    {
        cv::Mat img = cv::imread("dog.jpg");
        cv::resize(img,img,cv::Size(1920,1080));
        cv::Mat nv21;
        bgr2nv21(img,nv21);
        cv::Mat rgb(1080, 1920, CV_8UC3);
        int64 start = cv::getTickCount();
	    ncnn::yuv420sp2rgb((const unsigned char*)nv21.data, 1920, 1080, rgb.data);
        int64 end = cv::getTickCount();
        std::cout<<"one iter time: "<<(end-start) * 1000 / cv::getTickFrequency()<<std::endl;
        total_time += (end-start) * 1000 / cv::getTickFrequency();
    }
    std::cout<<"avg time: "<<total_time/50<<std::endl;
    
    return 0;
}