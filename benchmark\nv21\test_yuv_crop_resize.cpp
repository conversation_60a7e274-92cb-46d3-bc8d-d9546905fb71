#include<iostream>
#include <vector>
#include <fstream>
#include <string>
#include<opencv2/opencv.hpp>
#include<linux/videodev2.h>
using namespace std;
using namespace cv;


void bgr2nv21(cv::Mat bgr, cv::Mat& nv21)
{
    cv::Mat yuv;
    cv::cvtColor(bgr, yuv, cv::COLOR_BGR2YUV_YV12);
   
    nv21 = cv::Mat::zeros(yuv.rows, yuv.cols, CV_8UC1);
    memcpy(nv21.data, yuv.data, bgr.rows * bgr.cols);
    unsigned char* pnv21v = (unsigned char*)nv21.data + bgr.rows * bgr.cols;
    unsigned char* pnv21u = (unsigned char*)nv21.data + bgr.rows * bgr.cols + 1;
    unsigned char* pyuv_v = (unsigned char*)yuv.data + bgr.rows * bgr.cols;
    unsigned char* pyuv_u = (unsigned char*)yuv.data + bgr.rows * bgr.cols + bgr.rows * bgr.cols / 4;
    for (size_t i = 0; i < bgr.rows * bgr.cols / 4; i++)
    {
        *pnv21v = *pyuv_v;
        *pnv21u = *pyuv_u;

        pyuv_v++;
        pyuv_u++;
        pnv21v += 2;
        pnv21u += 2;
    }
}

static void nv21resize(cv::Mat& nv21, cv::Mat& dstnv21, int dstwidth, int dstheight, const cv::Rect& rect, const cv::Rect& dstrect)
{
    int rows = nv21.rows * 2 / 3;
    int cols = nv21.cols;
    cv::Mat y = cv::Mat(rows, cols, CV_8UC1, nv21.data);
    cv::Mat vu = cv::Mat(rows / 2, cols / 2, CV_8UC2, ((unsigned char*)nv21.data + rows * cols));

    cv::Mat dsty = cv::Mat(dstheight, dstwidth, CV_8UC1, dstnv21.data);
    cv::Mat dstvu = cv::Mat(dstheight / 2, dstwidth / 2, CV_8UC2, (unsigned char*)dstnv21.data + dstwidth * dstheight);
 
    cv::resize(y(rect), dsty(dstrect), cv::Size(dstrect.width, dstrect.height));
    cv::Rect vurect(rect.x / 2, rect.y / 2, rect.width / 2, rect.height / 2);   
    cv::Rect dstvurect(dstrect.x / 2, dstrect.y / 2, dstrect.width / 2, dstrect.height / 2);
    cv::resize(vu(vurect), dstvu(dstvurect), cv::Size(dstvurect.width, dstvurect.height));   
}


int main()
{
    float total_time = 0;
    for (int i = 0; i < 50; i++)
    {
        cv::setNumThreads(1);
        cv::Mat img = cv::imread("dog.jpg");
        cv::resize(img, img, cv::Size(1920, 1080));

        cv::Mat nv21;
        bgr2nv21(img, nv21);

        int dstwidth = 1920;
        int dstheight = 1080;

        cv::Mat dstnv21 = cv::Mat::zeros(dstheight * 3 / 2, dstwidth, CV_8UC1);

        double t1 = cv::getTickCount();
        cv::Rect rect(0, 0, 1918, 1078);
        cv::Rect dstrect(0, 0, dstwidth, dstheight);
        nv21resize(nv21, dstnv21, dstwidth, dstheight, rect, dstrect);
        double t2 = cv::getTickCount();
        double one_iter = (t2-t1) * 1000 / cv::getTickFrequency();
        cv::imwrite("w.jpg",dstnv21);
        std::cout<<one_iter<<std::endl;
        total_time+=one_iter;
    }
    std::cout<<total_time/50<<std::endl;
 
    return 0;
}