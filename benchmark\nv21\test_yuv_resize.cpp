#include<iostream>
#include <vector>
#include <fstream>
#include <string>
#include<opencv2/opencv.hpp>
#include<linux/videodev2.h>
using namespace std;
using namespace cv;


void bgr2nv21(cv::Mat bgr, cv::Mat& nv21)
{
    cv::Mat yuv;
    cv::cvtColor(bgr, yuv, cv::COLOR_BGR2YUV_YV12);
   
    nv21 = cv::Mat::zeros(yuv.rows, yuv.cols, CV_8UC1);
    memcpy(nv21.data, yuv.data, bgr.rows * bgr.cols);
    unsigned char* pnv21v = (unsigned char*)nv21.data + bgr.rows * bgr.cols;
    unsigned char* pnv21u = (unsigned char*)nv21.data + bgr.rows * bgr.cols + 1;
    unsigned char* pyuv_v = (unsigned char*)yuv.data + bgr.rows * bgr.cols;
    unsigned char* pyuv_u = (unsigned char*)yuv.data + bgr.rows * bgr.cols + bgr.rows * bgr.cols / 4;
    for (size_t i = 0; i < bgr.rows * bgr.cols / 4; i++)
    {
        *pnv21v = *pyuv_v;
        *pnv21u = *pyuv_u;

        pyuv_v++;
        pyuv_u++;
        pnv21v += 2;
        pnv21u += 2;
    }
}


int main()
{
    for (int i = 0; i < 50; i++)
    {
        cv::setNumThreads(1);
        cv::Mat img = cv::imread("dog.jpg");
        cv::resize(img, img, cv::Size(1920, 1080));

        cv::Mat nv21;
        bgr2nv21(img, nv21);

        cv::Mat y = cv::Mat(img.rows, img.cols, CV_8UC1, nv21.data);
        cv::Mat vu = cv::Mat(img.rows / 2, img.cols / 2, CV_8UC2, ((unsigned char*)nv21.data + img.rows * img.cols));
        int dstwidth = 640;
        int dstheight = 480;
        cv::Mat dstnv21 = cv::Mat::zeros(dstheight * 3 / 2, dstwidth, CV_8UC1);
        cv::Mat dsty = cv::Mat(dstheight, dstwidth, CV_8UC1, dstnv21.data);
        cv::Mat dstvu = cv::Mat(dstheight / 2, dstwidth / 2, CV_8UC2, (unsigned char*)dstnv21.data + dstwidth * dstheight);

        double t1 = cv::getTickCount();
        cv::resize(y, dsty, cv::Size(dsty.cols, dsty.rows));
        cv::resize(vu, dstvu, cv::Size(dstvu.cols, dstvu.rows));
        double t2 = cv::getTickCount();

        cv::Mat dstbgr;
        cv::cvtColor(dstnv21, dstbgr, cv::COLOR_YUV2BGR_NV21);
        std::cout<<(t2-t1) * 1000 / cv::getTickFrequency()<<std::endl;
    }

    return 0;
}