/*
 * Copyright (C) 2018  Fuzhou Rockchip Electronics Co., Ltd. All rights reserved.
 * Authors: <AUTHORS>
 *     libin <<EMAIL>>
 *
 * This program is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with this program.  If not, see <http://www.gnu.org/licenses/>.
 *
 * BY DOWNLOADING, INSTALLING, COPYING, SAVING OR OTHERWISE USING THIS SOFTWARE,
 * YOU ACKNOWLEDGE THAT YOU AGREE THE SOFTWARE RECEIVED FORM ROCKCHIP IS PROVIDED
 * TO YOU ON AN "AS IS" BASIS and ROCKCHP DISCLAIMS ANY AND ALL WARRANTIES AND
 * REPRESENTATIONS WITH RESPECT TO SUCH FILE, WHETHER EXPRESS, IMPLIED, STATUTORY
 * OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY IMPLIED WARRANTIES OF TITLE,
 * NON-INFRINGEMENT, MERCHANTABILITY, SATISFACTROY QUALITY, ACCURACY OR FITNESS FOR
 * A PARTICULAR PURPOSE.
 */

#define LOG_NDEBUG 0
#define LOG_TAG "rgaCopyScale"

#include <stdint.h>
#include <sys/types.h>
#include <math.h>
#include <fcntl.h>
#include <utils/misc.h>
#include <signal.h>
#include <time.h>

#include <cutils/properties.h>

#ifndef ANDROID_8

#include <binder/IPCThreadState.h>

#endif
#include <utils/Atomic.h>
#include <utils/Errors.h>
#include <utils/Log.h>

#include <ui/PixelFormat.h>
#include <ui/Rect.h>
#include <ui/Region.h>
#include <ui/DisplayInfo.h>
#include <ui/GraphicBufferMapper.h>
#include <RockchipRga.h>

#include <gui/ISurfaceComposer.h>
#include <gui/Surface.h>
#include <gui/SurfaceComposerClient.h>

#include <GLES/gl.h>
#include <GLES/glext.h>
#include <EGL/eglext.h>

#include <stdint.h>
#include <sys/types.h>

#include <system/window.h>

#include <utils/Thread.h>

#include <EGL/egl.h>
#include <GLES/gl.h>

///////////////////////////////////////////////////////
//#include "../drmrga.h"
#include <hardware/hardware.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include <unistd.h>

#include <sys/mman.h>
#include <linux/stddef.h>
#include "RockchipFileOps.h"
///////////////////////////////////////////////////////


using namespace android;
extern "C"
{

int rgaScale(buffer_handle_t srchandle, int srcfd, buffer_handle_t dsthandle, int dstfd,
    int srcWidth, int srcHeight, int srcFormat, 
    int rectx, int recty, int rectw, int recth,
    int dstWidth, int dstHeight, int dstFormat,
    int dstrectx, int dstrecty, int dstrectw, int dstrecth);
int rgaCopy(buffer_handle_t srchandle, int srcfd, buffer_handle_t dsthandle, int dstfd, int srcWidth, int srcHeight, int srcFormat,
    int dstWidth, int dstHeight, int dstFormat);
int rgaMerge(buffer_handle_t srchandle, int srcfd, buffer_handle_t dsthandle, int dstfd,
    int srcWidth, int srcHeight, int srcFormat, 
    int dstWidth, int dstHeight, int dstFormat, int ratio);

void* rga_malloc(void** vir_addr, int* phy_addr, int size)
{
    int ret = 0;

    if (NULL == vir_addr || NULL == phy_addr || size <= 0)
    {
        ALOGE("param is wrong: vir_addr[%p], phy_addr[%p], size[%d]\n", vir_addr, phy_addr, size);
        return NULL;
    }

#ifdef ANDROID_7_DRM
    GraphicBuffer* gb = new GraphicBuffer(size / 2,1,HAL_PIXEL_FORMAT_RGB_565,
        GRALLOC_USAGE_SW_WRITE_OFTEN | GRALLOC_USAGE_SW_READ_OFTEN | GRALLOC_USAGE_HW_FB);
#else
    GraphicBuffer* gb = new GraphicBuffer(size / 2,1,HAL_PIXEL_FORMAT_RGB_565,
        GRALLOC_USAGE_SW_WRITE_OFTEN | GRALLOC_USAGE_SW_READ_OFTEN | GRALLOC_USAGE_HW_FB);
#endif
    gb->incStrong(gb);
    if (gb->initCheck()) {
        ALOGE("GraphicBuffer error : %s\n",strerror(errno));
        return NULL;
    } 

    RockchipRga& rkRga(RockchipRga::get());

    GraphicBufferMapper &mgbMapper = GraphicBufferMapper::get();
    mgbMapper.registerBuffer(gb->handle);

    ret = gb->lock(GRALLOC_USAGE_SW_WRITE_OFTEN, vir_addr);
    if (ret) {
        ALOGE("lock buffer_dst error : %s\n",strerror(errno));
        return NULL;
    } 

    ret = gb->unlock();
    if (ret) {
        ALOGE("unlock buffer_dst error : %s\n",strerror(errno));
        return NULL;
    }

    ret = rkRga.RkRgaGetBufferFd(gb->handle, phy_addr);
    if (ret) {
        ALOGE("rgaGetdstFd error : %s,hnd=%p\n",
            strerror(errno),(void*)(gb->handle));
    }
    printf("malloc: %p\n", gb);
    return reinterpret_cast<void*>(gb);
}

int rga_free(void* handle)
{
    if (handle == NULL)
    {
        ALOGE("handle is invalid\n");
        return -1;
    }
    GraphicBuffer* gb = reinterpret_cast<GraphicBuffer*>(handle);
    printf("free: %p\n", gb);
    GraphicBufferMapper &mgbMapper = GraphicBufferMapper::get();
    printf("unregisterbuffer\n");
    mgbMapper.unregisterBuffer(gb->handle);
    printf("decstrong\n");
    gb->decStrong(gb);

    return 0;
}

int rgaConvert(buffer_handle_t srchandle, int srcfd, buffer_handle_t dsthandle, int dstfd, int srcWidth, int srcHeight, int srcFormat,int dstWidth, int dstHeight, int dstFormat)
{
    int ret = 0;

    RockchipRga& rkRga(RockchipRga::get());

    rga_info_t src;
    rga_info_t dst;

    memset(&src, 0, sizeof(rga_info_t));
    src.fd = srcfd;
    src.mmuFlag = ((2 & 0x3) << 4) | 1 | (1 << 8) | (1 << 10);

    memset(&dst, 0, sizeof(rga_info_t));
    dst.fd = dstfd;
    dst.mmuFlag = ((2 & 0x3) << 4) | 1 | (1 << 8) | (1 << 10);

    if (src.fd == -1 && srchandle != NULL)
	{
        /********** get src_Fd **********/
        ret = rkRga.RkRgaGetBufferFd(srchandle, &src.fd);
        //printf("src.fd =%d\n",src.fd);
        if (ret) {
            ALOGE("rgaGetsrcFd fail : %s,hnd=%p \n",
                                        strerror(errno),(void*)(srchandle));
	    }
            
	}

    if (dst.fd == -1 && dsthandle != NULL)
    {
        ret = rkRga.RkRgaGetBufferFd(dsthandle, &dst.fd);
        if (ret) {
            ALOGD("rgaGetdstFd error : %s,hnd=%p\n",
                                            strerror(errno),(void*)(dsthandle));
        }
    }

    rga_set_rect(&src.rect, 0,0,srcWidth,srcHeight,srcWidth/*stride*/,srcHeight,srcFormat);
    rga_set_rect(&dst.rect, 0,0,dstWidth,dstHeight,dstWidth/*stride*/,dstHeight,dstFormat);
    ret = rkRga.RkRgaBlit(&src, &dst, NULL);
    if (ret) {
        ALOGE("RKRgaBlit error : %s\n", strerror(errno));
    }
    return ret;
}

int color_fill_rgb(int fd, int src_width, int src_height, int bcolor, int gcolor, int rcolor)
{
    int ret;
    RockchipRga& rkRga(RockchipRga::get());
    rga_info_t dst;

    memset(&dst, 0, sizeof(rga_info_t));
    dst.fd = fd;
    dst.mmuFlag = ((2 & 0x3) << 4) | 1 | (1 << 8) | (1 << 10);
    //dst.hnd = gbd->handle;

    /********** set the rect_info **********/
    rga_set_rect(&dst.rect, 0,0,src_width,src_height,src_width/*stride*/,src_height,HAL_PIXEL_FORMAT_RGB_888);

    /************ set the rga_mod  **********/
    dst.color = (bcolor << 16) + (gcolor << 8) + rcolor; //bgr

    ret = rkRga.RkRgaCollorFill(&dst);
    if (ret) {
        printf("rgaFillColor error : %s\n",
                                            strerror(errno));
    }
    return ret;   
}
int nv12torgb(int srcfd, int dstfd, int src_width, int src_height, int dst_width, int dst_height)
{  
    int ret = rgaConvert(NULL, srcfd, NULL, dstfd, src_width, src_height, HAL_PIXEL_FORMAT_YCrCb_NV12, dst_width, dst_height, HAL_PIXEL_FORMAT_RGB_888);

    return ret;
}
#if 0
int main()
{
    void* vir_addr = NULL;
    int phy_addr = -1;
    void* vir_addr2 = NULL;
    int phy_addr2 = -1;
    
    void* handle1 = rga_malloc(&vir_addr, &phy_addr, 128 * 128 * 3 / 2);
    void* handle2 = rga_malloc(&vir_addr2, &phy_addr2, 128 * 128 * 3);
    nv12torgb(phy_addr, phy_addr2, 128, 128, 128, 128);
    ALOGE("free\n");
    rga_free(handle1);
    rga_free(handle2);
    return 1;
}
#endif
#ifdef ANDROID_7_DRM
    sp<GraphicBuffer> gbtemp;
    sp<GraphicBuffer> gbrgb;
    sp<GraphicBuffer> gbblack;
#else
    sp<GraphicBuffer> gbtemp;
    sp<GraphicBuffer> gbrgb;
    sp<GraphicBuffer> gbblack;
#endif

int sysWidth,sysHeight,sysFormat;
int tempWidth,tempHeight,tempFormat;
int interFormat;

int init(int width, int height, void** vir_addr)
{
    int ret = 0;
    //char* buf = NULL;
    /********** SrcInfo set **********/
    sysWidth = width;
    sysHeight = height;
    sysFormat = HAL_PIXEL_FORMAT_YCrCb_NV12;

    /********** DstInfo set **********/
    tempWidth = width;
    tempHeight = height;
    tempFormat = HAL_PIXEL_FORMAT_YCrCb_NV12;

    *vir_addr = NULL;
    interFormat = HAL_PIXEL_FORMAT_RGB_888;

#if 0
    GraphicBufferMapper &mgbMapper = GraphicBufferMapper::get();

    /********** apply for dst_buffer **********/
#ifdef ANDROID_7_DRM
    gbtemp = new GraphicBuffer(tempWidth,tempHeight,tempFormat,
        GRALLOC_USAGE_SW_WRITE_OFTEN | GRALLOC_USAGE_SW_READ_OFTEN | GRALLOC_USAGE_HW_FB);

    gbrgb = new GraphicBuffer(sysWidth,sysHeight,interFormat,
        GRALLOC_USAGE_SW_WRITE_OFTEN | GRALLOC_USAGE_SW_READ_OFTEN | GRALLOC_USAGE_HW_FB);

    gbblack = new GraphicBuffer(sysWidth,sysHeight,interFormat,
        GRALLOC_USAGE_SW_WRITE_OFTEN | GRALLOC_USAGE_SW_READ_OFTEN | GRALLOC_USAGE_HW_FB);
#else
    gbtemp = new GraphicBuffer(tempWidth,tempHeight,tempFormat,
        GRALLOC_USAGE_SW_WRITE_OFTEN | GRALLOC_USAGE_SW_READ_OFTEN);

    gbrgb = new GraphicBuffer(sysWidth,sysHeight,interFormat,
        GRALLOC_USAGE_SW_WRITE_OFTEN | GRALLOC_USAGE_SW_READ_OFTEN | GRALLOC_USAGE_HW_FB);

    gbblack = new GraphicBuffer(sysWidth,sysHeight,interFormat,
        GRALLOC_USAGE_SW_WRITE_OFTEN | GRALLOC_USAGE_SW_READ_OFTEN | GRALLOC_USAGE_HW_FB);
#endif

    if (gbtemp->initCheck()) {
        printf("GraphicBuffer_dst error : %s\n",strerror(errno));
        return ret;
    } else
        printf("GraphicBuffer_dst %s \n","ok");

    if (gbrgb->initCheck()) {
        printf("GraphicBuffer_rgb error : %s\n",strerror(errno));
        return ret;
    } else
        printf("GraphicBuffer_rgb %s \n","ok");
    
    if (gbblack->initCheck()) {
        printf("GraphicBuffer_bblack error : %s\n",strerror(errno));
        return ret;
    } else
        printf("GraphicBuffer_bblack %s \n","ok");

    /********** map buffer_address to userspace **********/

#ifdef ANDROID_8
    buffer_handle_t importedHandle_dst;
    mgbMapper.importBuffer(gbtemp->handle, &importedHandle_dst);
    gbtemp->handle = importedHandle_dst;
#else
    mgbMapper.registerBuffer(gbtemp->handle);

    mgbMapper.registerBuffer(gbrgb->handle);
    mgbMapper.registerBuffer(gbblack->handle);
#endif

    /********** write data to dst_buffer or init buffer **********/
    ret = gbtemp->lock(GRALLOC_USAGE_SW_WRITE_OFTEN, vir_addr);
    if (ret) {
        printf("lock buffer_dst error : %s\n",strerror(errno));
        return ret;
    } else
        printf("lock buffer_dst %s \n","ok");

    /********** buffer initialize **********/
    /* Notice :
     *          RGBA8888 4 bytes
     *          YUV      1.5 bytes
     *          RGB565   2 bytes
     * so,if you want to memset YUV_buffer please 1.5*xx*xx
     */
    //memset(buf,0x00,4*1920*1088);

    ret = gbtemp->unlock();
    if (ret) {
        printf("unlock buffer_dst error : %s\n",strerror(errno));
        return ret;
    } else
        printf("unlock buffer_dst %s \n","ok");
    
    ret = gbblack->lock(GRALLOC_USAGE_SW_WRITE_OFTEN, (void**)&buf);
    if (ret) {
        printf("lock buffer_black error : %s\n",strerror(errno));
        return ret;
    } else
        printf("lock buffer_black %s \n","ok");

    memset(buf, 0, sysWidth * sysHeight * 3);
    ret = gbblack->unlock();
    if (ret) {
        printf("unlock buffer_black error : %s\n",strerror(errno));
        return ret;
    } else
        printf("unlock buffer_black %s \n","ok");
#endif
    return ret;
}

int g_debug = 0;

int copy(int bufferfd)
{
    int ret = rgaCopy(NULL, bufferfd, gbtemp->handle, -1, tempWidth, tempHeight, tempFormat, sysWidth, sysHeight, sysFormat);
    return ret;
}

int rgaCopy(buffer_handle_t srchandle, int srcfd, buffer_handle_t dsthandle, int dstfd, int srcWidth, int srcHeight, int srcFormat,
    int dstWidth, int dstHeight, int dstFormat)
{
    int ret;
    RockchipRga& rkRga(RockchipRga::get());
    
    /********** rga_info_t Init **********/
    rga_info_t src;
    rga_info_t dst;

    memset(&src, 0, sizeof(rga_info_t));
    src.fd = srcfd;
    src.mmuFlag = ((2 & 0x3) << 4) | 1 | (1 << 8) | (1 << 10);
    src.hnd = 0;// gbs->handle;

    memset(&dst, 0, sizeof(rga_info_t));
    dst.fd = dstfd;
    dst.mmuFlag = ((2 & 0x3) << 4) | 1 | (1 << 8) | (1 << 10);
    dst.hnd = 0;//gbd->handle;


    /********** get src_Fd **********/
    if (src.fd == -1 && srchandle != NULL)
    {
        ret = rkRga.RkRgaGetBufferFd(srchandle, &src.fd);
        //printf("src.fd =%d\n",src.fd);
        if (ret) {
            printf("rgaGetsrcFd fail : %s,hnd=%p \n",
                                        strerror(errno),(void*)(srchandle));
            ALOGD("rgaGetsrcFd fail : %s,hnd=%p \n",
                                        strerror(errno),(void*)(srchandle));
        }
    }
        
    /********** get dst_Fd **********/
    if (dst.fd == -1 && dsthandle != NULL)
    {
        ret = rkRga.RkRgaGetBufferFd(dsthandle, &dst.fd);
        //printf("dst.fd =%d \n",dst.fd);
        if (g_debug)
        {
            ALOGD("dst.fd =%d\n",dst.fd);
        }
        if (ret) {
            printf("rgaGetdstFd error : %s,hnd=%p\n",
                                            strerror(errno),(void*)(dsthandle));
            ALOGD("rgaGetdstFd error : %s,hnd=%p\n",
                                            strerror(errno),(void*)(dsthandle));
        }
    }

    rga_set_rect(&src.rect, 0,0,srcWidth,srcHeight,srcWidth/*stride*/,srcHeight,srcFormat);
    rga_set_rect(&dst.rect, 0,0,dstWidth,dstHeight,dstWidth/*stride*/,dstHeight,dstFormat);

    /************ set the rga_mod ,rotation\composition\scale\copy .... **********/

    /********** call rga_Interface **********/
    struct timeval tpend1, tpend2;
    double usec1 = 0;
    gettimeofday(&tpend1, NULL);

    ret = rkRga.RkRgaBlit(&src, &dst, NULL);

    gettimeofday(&tpend2, NULL);
    usec1 = 1000.0 * (tpend2.tv_sec - tpend1.tv_sec) + (tpend2.tv_usec - tpend1.tv_usec) / 1000.0;
    //printf("cost_time=%f ms\n", usec1);
    if (g_debug)
    {
        ALOGD("cost_time=%f ms\n", usec1);
    }

    if (ret) {
        printf("rgaFillColor error : %s,hnd=%d\n",
                                            strerror(errno), dst.fd);
        ALOGD("rgaFillColor error : %s,hnd=%d\n",
                                            strerror(errno), dst.fd);
    }

    return 0;
}


int scale(int bufferfd, int rectx, int recty, int rectw, int recth)
{
    if (g_debug)
    {
        ALOGD("rgaScale");
    }	

    int ret = rgaScale(gbtemp->handle, -1, NULL, bufferfd, sysWidth, sysHeight, sysFormat, rectx, recty, rectw, recth, tempWidth, tempHeight, tempFormat, 0, 0, tempWidth, tempHeight);
    return ret;
}

int crop_scale(int srcfd, int dstfd, int src_width, int src_height, int rectx, int recty, int rectw, int recth, int dst_width, int dst_height)
{
    if (g_debug)
    {
        ALOGD("crop_scale");
    }
   
    int ret = rgaScale(NULL, srcfd, NULL, dstfd, src_width, src_height, HAL_PIXEL_FORMAT_YCrCb_NV12, rectx, recty, rectw, recth, dst_width, dst_height, HAL_PIXEL_FORMAT_YCrCb_NV12, 0, 0, dst_width, dst_height);

    return ret;
}


int crop_scale_rgb(int srcfd, int dstfd, int src_width, int src_height, int rectx, int recty, int rectw, int recth, int dst_width, int dst_height,int dstrectx, int dstrecty, int dstrectw, int dstrecth)
{
    int ret = rgaScale(NULL, srcfd, NULL, dstfd, src_width, src_height, HAL_PIXEL_FORMAT_RGB_888, rectx, recty, rectw, recth, dst_width, dst_height, HAL_PIXEL_FORMAT_RGB_888, dstrectx, dstrecty, dstrectw, dstrecth);

    return ret;
}

int rgaScale(buffer_handle_t srchandle, int srcfd, buffer_handle_t dsthandle, int dstfd,
    int srcWidth, int srcHeight, int srcFormat, 
    int rectx, int recty, int rectw, int recth,
    int dstWidth, int dstHeight, int dstFormat,
    int dstrectx, int dstrecty, int dstrectw, int dstrecth)
{
    int ret;
    RockchipRga& rkRga(RockchipRga::get());

    /********** rga_info_t Init **********/
    rga_info_t src;
    rga_info_t dst;

    memset(&src, 0, sizeof(rga_info_t));
    src.fd = srcfd;
    src.mmuFlag = ((2 & 0x3) << 4) | 1 | (1 << 8) | (1 << 10);
    src.hnd = 0;// gbs->handle;

    memset(&dst, 0, sizeof(rga_info_t));
    dst.fd = dstfd;
    dst.mmuFlag = ((2 & 0x3) << 4) | 1 | (1 << 8) | (1 << 10);
    dst.hnd = 0;//gbd->handle;

	if (src.fd == -1 && srchandle != NULL)
	{
            /********** get src_Fd **********/
            ret = rkRga.RkRgaGetBufferFd(srchandle, &src.fd);
            //printf("src.fd =%d\n",src.fd);
            if (ret) {
                printf("rgaGetsrcFd fail : %s,hnd=%p \n",
                                            strerror(errno),(void*)(srchandle));
                ALOGD("rgaGetsrcFd fail : %s,hnd=%p \n",
                                            strerror(errno),(void*)(srchandle));
	    }
            
	}

    if (dst.fd == -1 && dsthandle != NULL)
	{
        ret = rkRga.RkRgaGetBufferFd(dsthandle, &dst.fd);
        //printf("dst.fd =%d \n",dst.fd);
        if (g_debug)
        {
            ALOGD("dst.fd =%d\n",dst.fd);
        }
        if (ret) {
            printf("rgaGetdstFd error : %s,hnd=%p\n",
                                            strerror(errno),(void*)(dsthandle));
            ALOGD("rgaGetdstFd error : %s,hnd=%p\n",
                                            strerror(errno),(void*)(dsthandle));
        }
	}

    rga_set_rect(&src.rect, rectx,recty,rectw,recth,srcWidth/*stride*/,srcHeight,srcFormat);
    rga_set_rect(&dst.rect, dstrectx, dstrecty, dstrectw, dstrecth,dstWidth/*stride*/,dstHeight,dstFormat);

    /********** call rga_Interface **********/
    struct timeval tpend1, tpend2;
    double usec1 = 0;
    gettimeofday(&tpend1, NULL);

    ret = rkRga.RkRgaBlit(&src, &dst, NULL);

    gettimeofday(&tpend2, NULL);
    usec1 = 1000.0 * (tpend2.tv_sec - tpend1.tv_sec) + (tpend2.tv_usec - tpend1.tv_usec) / 1000.0;
    //printf("cost_time=%f ms\n", usec1);
    if (g_debug)
	{
	    ALOGD("cost_time=%f ms\n", usec1);
    }

	if (ret) {
        printf("rgaFillColor error : %s,hnd=%d\n",
                                        strerror(errno), dst.fd);
        ALOGD("rgaFillColor error : %s,hnd=%d\n",
                                        strerror(errno), dst.fd);
	}
       
    return ret;
}

int merge(int srcfd, int ratio)
{
    if (g_debug)
    {
        ALOGD("crop_scale");
    }
   
    int ret = rgaMerge(NULL, srcfd, NULL, srcfd, sysWidth, sysHeight, sysFormat, sysWidth, sysHeight, sysFormat, ratio);

    return ret;
}

int rgaMerge(buffer_handle_t srchandle, int srcfd, buffer_handle_t dsthandle, int dstfd,
    int srcWidth, int srcHeight, int srcFormat, 
    int dstWidth, int dstHeight, int dstFormat, int ratio)
{
    int ret;
    RockchipRga& rkRga(RockchipRga::get());

	if (srcfd == -1 && srchandle != NULL)
	{
        /********** get src_Fd **********/
        ret = rkRga.RkRgaGetBufferFd(srchandle, &srcfd);
        if (ret) {
            printf("rgaGetsrcFd fail : %s,hnd=%p \n",
                                        strerror(errno),(void*)(srchandle));
            ALOGD("rgaGetsrcFd fail : %s,hnd=%p \n",
                                        strerror(errno),(void*)(srchandle));
	    }          
	}

    if (dstfd == -1 && dsthandle != NULL)
	{
        /********** get src_Fd **********/
        ret = rkRga.RkRgaGetBufferFd(dsthandle, &dstfd);
        if (ret) {
            printf("rgaGetdstFd fail : %s,hnd=%p \n",
                                        strerror(errno),(void*)(dsthandle));
            ALOGD("rgaGetdstFd fail : %s,hnd=%p \n",
                                        strerror(errno),(void*)(dsthandle));
	    }          
	}

    int rgbfd = -1;
    int blackfd = -1;

    ret = rkRga.RkRgaGetBufferFd(gbrgb->handle, &rgbfd);
    //printf("rgbfd =%d \n", rgbfd);
    //ALOGD("rgbfd =%d\n", rgbfd);
    if (ret) {
        printf("rgaGet rgbfd error : %s,hnd=%p\n",
                                        strerror(errno),(void*)(gbrgb->handle));
    }

    /********** get black_Fd **********/
    ret = rkRga.RkRgaGetBufferFd(gbblack->handle, &blackfd);
    //printf("blackfd =%d \n",blackfd);
    //ALOGD("blackfd =%d\n",blackfd);
    if (ret) {
        printf("rgaGet blackfd error : %s,hnd=%p\n",
                                        strerror(errno),(void*)(gbblack->handle));
    } 

    /********** rga_info_t Init **********/
    rga_info_t src;
    rga_info_t dst;

    memset(&src, 0, sizeof(rga_info_t));
    src.fd = srcfd;
    src.mmuFlag = 1;//((2 & 0x3) << 4) | 1 | (1 << 8) | (1 << 10);
    src.hnd = 0;// gbs->handle;

    memset(&dst, 0, sizeof(rga_info_t));
    dst.fd = rgbfd;
    dst.mmuFlag = 1;//((2 & 0x3) << 4) | 1 | (1 << 8) | (1 << 10);
    dst.hnd = 0;//gbd->handle;

    rga_set_rect(&src.rect, 0,0,srcWidth,srcHeight,srcWidth/*stride*/,srcHeight,srcFormat);
    rga_set_rect(&dst.rect, 0,0,dstWidth,dstHeight,dstWidth/*stride*/,dstHeight,interFormat);
 
    /********** call rga_Interface **********/
    struct timeval tpend1, tpend2;
    double usec1 = 0;
    gettimeofday(&tpend1, NULL);

    ret = rkRga.RkRgaBlit(&src, &dst, NULL);
    if (ret) {
        printf("rgaMerge step1 error : %s,hnd=%d\n",
                                        strerror(errno), dst.fd);
        ALOGD("rgaMerge step1 error : %s,hnd=%d\n",
                                        strerror(errno), dst.fd);
	}

    src.fd = blackfd;
	src.rect.format = interFormat;
	src.blend = 0x0105 + (ratio << 16);
	dst.fd = rgbfd;
	dst.rect.format = interFormat;

    ret = rkRga.RkRgaBlit(&src, &dst, NULL);

    if (ret) {
        printf("rgaMerge step2 error : %s,hnd=%d\n",
                                        strerror(errno), dst.fd);
        ALOGD("rgaMerge step2 error : %s,hnd=%d\n",
                                        strerror(errno), dst.fd);
	}

    src.fd = rgbfd;
	src.blend = 0;
	dst.fd = dstfd;
	dst.rect.format = dstFormat;
    dst.mmuFlag = ((2 & 0x3) << 4) | 1 | (1 << 8) | (1 << 10);

    ret = rkRga.RkRgaBlit(&src, &dst, NULL);

    if (ret) {
        printf("rgaMerge step3 error : %s,hnd=%d\n",
                                        strerror(errno), dst.fd);
        ALOGD("rgaMerge step3 error : %s,hnd=%d\n",
                                        strerror(errno), dst.fd);
	}

    gettimeofday(&tpend2, NULL);
    usec1 = 1000.0 * (tpend2.tv_sec - tpend1.tv_sec) + (tpend2.tv_usec - tpend1.tv_usec) / 1000.0;
    //printf("cost_time=%f ms\n", usec1);
    if (g_debug)
	{
	    ALOGD("cost_time=%f ms\n", usec1);
    }
         
    return 0;
}

}
