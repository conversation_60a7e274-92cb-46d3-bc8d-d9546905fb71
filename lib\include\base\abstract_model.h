// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_CAMERA_ABSTRACT_MODEL_H
#define CZCV_CAMERA_ABSTRACT_MODEL_H

#include "status.h"
#include "profile_data.h"
#include "dynamic_param.h"
#include "common.h"

namespace  czcv_camera
{
    class PUBLIC AbstarctModel
    {
    public:
        virtual  ~AbstarctModel(){}

        //AbstarctModel(const AbstarctModel &m) =delete;
        //AbstarctModel & operator=(const AbstarctModel &m)=delete;
        /**
         * @brief init model with config
         * @param modelConfig [in] model or config path
         * @return see Status,CZCV_OK if success
         */
        virtual Status init(std::vector<std::string> modelConfig);
        virtual Status init(std::vector<std::string> modelConfig,std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType);
        virtual Status release();
        /***************************************************/

        /**
         * @brief get profile data, e.g FPS
         * @return see @ProfileData
         */
        ProfileData get_frofile_data() const {return  _profileData;}
        /***************************************************/

    protected:
        ProfileData _profileData;///< profile data, e.g FPS

        std::vector<std::string> _modelConfig; ///< model path maybe
        int _only_cpu;
        czcv_model_type_t _modelType; ///< model type
        DynamicParams _params; ///< dynamic hyper params
        std::string _modelName;  ///<[optional] model name
    };
}

#endif //CZCV_CAMERA_ABSTRACT_MODEL_H
