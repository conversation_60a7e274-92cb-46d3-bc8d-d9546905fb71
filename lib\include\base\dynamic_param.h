﻿// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.
#pragma once
#ifndef CZCV_CAMERA_DYNAMIC_PARAM_H
#define CZCV_CAMERA_DYNAMIC_PARAM_H

#include <iostream>
#include <string>
#include <memory>
#include <utility>
#include <typeindex>
#include <map>
#include <vector>
#include <thread>
#include <mutex>
#include "macro.h"

namespace  czcv_camera
{
	// like c++17  std::any
	class PUBLIC  Any
	{
	public:
		Any(void) : m_tpIndex(std::type_index(typeid(void))) {}
		Any(const Any& that) : m_ptr(that.Clone()), m_tpIndex(that.m_tpIndex) {}
		Any(Any && that) : m_ptr(std::move(that.m_ptr)), m_tpIndex(that.m_tpIndex) {}

		//创建智能指针时，对于一般的类型，通过std::decay来移除引用和cv符，从而获取原始类型
		template<typename U, class = typename std::enable_if<!std::is_same<typename std::decay<U>::type, Any>::value, U>::type> Any(U && value) : m_ptr(new Derived < typename std::decay<U>::type>(std::forward<U>(value))),
			m_tpIndex(std::type_index(typeid(typename std::decay<U>::type))) {}

		bool IsNull() const { return !bool(m_ptr); }

		template<class U> bool Is() const
		{
			return m_tpIndex == std::type_index(typeid(U));
		}

		//将Any转换为实际的类型
		template<class U>
		U& AnyCast()
		{
			if (!Is<U>())
			{
				LOGE("[WARN] can not cast %s to %s, we use  reinterpret_cast",m_tpIndex.name(), typeid(U).name());
				//throw std::bad_cast();
                auto derived = reinterpret_cast<Derived<U>*> (m_ptr.get());
                return derived->m_value;
			}

			auto derived = dynamic_cast<Derived<U>*> (m_ptr.get());
			return derived->m_value;
		}

		Any& operator=(const Any& a)
		{
			if (m_ptr == a.m_ptr)
				return *this;

			m_ptr = a.Clone();
			m_tpIndex = a.m_tpIndex;
			return *this;
		}

		//private:
		struct Base;
		typedef std::unique_ptr<Base> BasePtr;

		struct Base
		{
			virtual ~Base() {}
			virtual BasePtr Clone() const = 0;
		};

		template<typename T>
		struct Derived : Base
		{
			template<typename U>
			Derived(U && value) : m_value(std::forward<U>(value)) { }

			BasePtr Clone() const
			{
				return BasePtr(new Derived<T>(m_value));
			}

			T m_value;
		};

		BasePtr Clone() const
		{
			if (m_ptr != nullptr)
				return m_ptr->Clone();

			return nullptr;
		}

		BasePtr m_ptr;
		std::type_index m_tpIndex;
		friend  class DynamicParams;
	};

	class PUBLIC  DynamicParams
	{
	public:
		DynamicParams() {}
		~DynamicParams() {}

		DynamicParams& operator=(DynamicParams& v)
		{
			std::vector<std::string> keys = v.keys();
			for (auto &key : keys)
			{
				this->set(key, v.get(key, Any()));
			}
			return *this;
		}
		std::mutex _mu;

		void set(std::string key, Any value)
		{
			std::lock_guard<std::mutex> lk(_mu);
			_keyValues[key] = value;
		}
		Any get(std::string key, Any defaultV)
		{
			//std::lock_guard<std::mutex> lk(_mu);
			auto iter = _keyValues.find(key);
			if (iter != _keyValues.end())
			{
				return iter->second;
			}
			else
			{
				_keyValues[key] = defaultV;
				return defaultV;
			}

		}

		bool has_key(std::string key)
		{
			std::lock_guard<std::mutex> lk(_mu);
			auto iter = _keyValues.find(key);
			if (iter != _keyValues.end())
			{
				return true;
			}
			return false;
		}
		std::vector<std::string>  keys()
		{
			std::lock_guard<std::mutex> lk(_mu);
			std::vector<std::string> keys;
			auto iter = _keyValues.begin();
			while (iter != _keyValues.end())
			{
				keys.push_back(iter->first);
				iter++;
			}
			return keys;
		}
		std::map<std::string, Any>  copy()
		{
			std::lock_guard<std::mutex> lk(_mu);
			return _keyValues;
		}
	private:
		std::map<std::string, Any>  _keyValues;
	};
}//namespace  czcv_camera

#endif //CZCV_CAMERA_DYNAMIC_PARAM_H
