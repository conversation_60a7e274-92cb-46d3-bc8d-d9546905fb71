// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_CAMERA_MEM_ALLOCATOR_H
#define CZCV_CAMERA_MEM_ALLOCATOR_H

#ifdef _WIN32
#include <windows.h>
#endif
#include "base/macro.h"
#include <stdlib.h>
#include <list>
#include <mutex>

namespace czcv_camera
{

#if __AVX__
    // the alignment of all the allocated buffers
#define MALLOC_ALIGN 256
#else
// the alignment of all the allocated buffers
#define MALLOC_ALIGN 16
#endif

    // Aligns a pointer to the specified number of bytes
    // ptr Aligned pointer
    // n Alignment size that must be a power of two
    template<typename _Tp>
    static inline _Tp* alignPtr(_Tp* ptr, int n = (int)sizeof(_Tp))
    {
        return (_Tp*)(((size_t)ptr + n - 1) & -n);
    }

    // Aligns a buffer size to the specified number of bytes
    // The function returns the minimum number that is greater or equal to sz and is divisible by n
    // sz Buffer size to align
    // n Alignment size that must be a power of two
    static inline size_t alignSize(size_t sz, int n)
    {
        return (sz + n - 1) & -n;
    }

    static inline void* fastMalloc(size_t size)
    {
#if _MSC_VER
        return _aligned_malloc(size, MALLOC_ALIGN);
#elif (defined(__unix__) || defined(__APPLE__)) && _POSIX_C_SOURCE >= 200112L || (__ANDROID__ && __ANDROID_API__ >= 17)
        void* ptr = 0;
    if (posix_memalign(&ptr, MALLOC_ALIGN, size))
        ptr = 0;
    return ptr;
#elif __ANDROID__ && __ANDROID_API__ < 17
        return memalign(MALLOC_ALIGN, size);
#else
        unsigned char* udata = (unsigned char*)malloc(size + sizeof(void*) + MALLOC_ALIGN);
        if (!udata)
            return 0;
        unsigned char** adata = alignPtr((unsigned char**)udata + 1, MALLOC_ALIGN);
        adata[-1] = udata;
        return adata;
#endif
    }

    static inline void fastFree(void* ptr)
    {
        if (ptr)
        {
#if _MSC_VER
            _aligned_free(ptr);
#elif (defined(__unix__) || defined(__APPLE__)) && _POSIX_C_SOURCE >= 200112L || (__ANDROID__ && __ANDROID_API__ >= 17)
            free(ptr);
#elif __ANDROID__ && __ANDROID_API__ < 17
            free(ptr);
#else
            unsigned char* udata = ((unsigned char**)ptr)[-1];
            free(udata);
#endif
        }
    }

    class PUBLIC Allocator
    {
    public:
        virtual ~Allocator();
        virtual void* fastMalloc(size_t size) = 0;
        virtual void fastFree(void* ptr) = 0;
    };

    class PUBLIC PoolAllocator : public Allocator
    {
    public:
        PoolAllocator();
        ~PoolAllocator();

        // ratio range 0 ~ 1
        // default cr = 0.75
        void set_size_compare_ratio(float scr);

        // release all budgets immediately
        void clear();

        virtual void* fastMalloc(size_t size);
        virtual void fastFree(void* ptr);

    private:
        std::mutex budgets_lock;
        std::mutex payouts_lock;
        unsigned int size_compare_ratio; // 0~256
        std::list<std::pair<size_t, void*> > budgets;
        std::list<std::pair<size_t, void*> > payouts;
    };

    class PUBLIC UnlockedPoolAllocator : public Allocator
    {
    public:
        UnlockedPoolAllocator();
        ~UnlockedPoolAllocator();

        // ratio range 0 ~ 1
        // default cr = 0.75
        void set_size_compare_ratio(float scr);

        // release all budgets immediately
        void clear();

        virtual void* fastMalloc(size_t size);
        virtual void fastFree(void* ptr);

    private:
        unsigned int size_compare_ratio; // 0~256
        std::list<std::pair<size_t, void*> > budgets;
        std::list<std::pair<size_t, void*> > payouts;
    };

} // namespace czcv_camera

#endif //CZCV_CAMERA_MEM_ALLOCATOR_H
