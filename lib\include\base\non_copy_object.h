// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_CAMERA_NON_COPY_OBJECT_H
#define CZCV_CAMERA_NON_COPY_OBJECT_H

#include "macro.h"

namespace  czcv_camera
{
    class PUBLIC NonCopyObject
    {
    public:
        NonCopyObject(){}
        virtual ~NonCopyObject(){}
        NonCopyObject(const NonCopyObject *b) = delete;
        NonCopyObject& operator =(const NonCopyObject *b) = delete;
    };
}//namespace  czcv_camera


#endif //CZCV_CAMERA_NON_COPY_OBJECT_H
