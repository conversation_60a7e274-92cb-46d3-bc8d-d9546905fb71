// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_CAMERA_STATUS_H
#define CZCV_CAMERA_STATUS_H
#include "macro.h"
#include <string>

namespace  czcv_camera
{
    enum StatusCode
    {
        CZCV_OK = 0x0,
        CZCV_PARAM_ERR = 0xA000,
        CZCV_MEM_ERR   = 0xA001,
        CZCV_CREATE_PROC_ERR = 0xA002,
        CZ<PERSON><PERSON>_RUN_KERNEL_ERR  = 0xA003,
        CZCV_TRACKER_INIT_FAILED = 0xA004,
        CZCV_TRACKER_UPDATE_FAILED = 0xA005,
        CZCV_PARSE_ERR = 0xA006,
        CZCV_INIT_MODEL_ERR = 0xA007,
        CZCV_SUBMIT_JOB_BUSY = 0xA008,

        CZCV_FRAME_DATA_INVALID = 0xB001,
        CZCV_SUBMIT_JOB_FAILED = 0xB003,
        CZCV_INFER_ENGINE_ERR = 0xB004

    };

    class PUBLIC Status
    {
    public:
        ~Status();
        Status(int code = CZCV_OK, std::string message = "OK");

        Status &operator=(int code);

        bool operator==(int code_);
        bool operator!=(int code_);
        operator int();
        operator bool();
        std::string description();

    private:
        int code_ = 0;
        std::string message_;
    };
}//namespace  czcv_camera

#endif //CZCV_CAMERA_STATUS_H
