// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.



#ifndef CZCV_CAMERA_THREAD_SAFE_QUEUE_H
#define CZCV_CAMERA_THREAD_SAFE_QUEUE_H

#include <iostream>
#include <thread>
#include <queue>
#include <mutex>
#include <memory>
#include <condition_variable>

namespace czcv_camera
{

    template<typename T>
    class threadsafe_queue
    {
    public:
        threadsafe_queue() {}
        ~threadsafe_queue() {}
        void push(T new_data)
        {
            std::lock_guard<std::mutex> lk(mut);
            data_queue.push(std::move(new_data));
            cond.notify_one();
        }
        void wait_and_pop(T& val)
        {
            std::unique_lock<std::mutex> ulk(mut);
            cond.wait(ulk, [this]() { return !data_queue.empty(); });
            val = std::move(data_queue.front());
            data_queue.pop();

        }
        std::shared_ptr<T> wait_and_pop()

        {
            std::unique_lock<std::mutex> ulk(mut);
            cond.wait(ulk, [this]() { return !data_queue.empty(); });
            std::shared_ptr<T> val(std::make_shared<T>(std::move(data_queue.front())));
            data_queue.pop();
            return val;

        }
        bool wait_pop(T &val)
        {
            std::unique_lock<std::mutex> ulk(mut);
            cond.wait(ulk, [this]() { return !data_queue.empty(); });
            val = std::move(data_queue.front());
            data_queue.pop();
            return true;
        }

        bool try_pop(T& val)
        {
            std::lock_guard<std::mutex> lk(mut);
            if (data_queue.empty())
                return false;
            val = std::move(data_queue.front());
            data_queue.pop();
            return true;
        }
        bool try_pop_with_copy(T& val)
        {
            std::lock_guard<std::mutex> lk(mut);
            if (data_queue.empty())
                return false;
            val = data_queue.front();
            data_queue.pop();
            return true;
        }

        std::shared_ptr<T> try_pop()
        {
            std::shared_ptr<T> val;
            std::lock_guard<std::mutex> lk(mut);
            if (data_queue.empty())
                return val;
            val = std::make_shared<T>(std::move(data_queue.front()));
            data_queue.pop();
            return val;

        }

        bool empty()
        {
            std::lock_guard<std::mutex> lk(mut);
            return data_queue.empty();
        }
        int size()
        {
            std::lock_guard<std::mutex> lk(mut);
            return data_queue.size();
        }

        std::queue<T> copy()
        {
            std::lock_guard<std::mutex> lk(mut);
            return data_queue;
        }
        void clear()
        {
            std::lock_guard<std::mutex> lk(mut);
            std::queue<T> empty;
            swap(empty, data_queue);
        }

    private:
        std::queue<T> data_queue;
        std::mutex mut;
        std::condition_variable cond;
    };

}//namespace czcv_camera

#endif //CZCV_CAMERA_THREAD_SAFE_QUEUE_H
