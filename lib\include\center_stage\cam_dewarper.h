// Copyright (C) 2021 CZUR Limited. All rights reserved.
// Only CZUR inner use.
// Author: lih<PERSON><PERSON><EMAIL>

#ifndef CZCV_CAMERA_CAM_DEWARPER_H
#define CZCV_CAMERA_CAM_DEWARPER_H

#include <base/common.h>
#include <base/status.h>

namespace  czcv_camera
{

    class PUBLIC BaseCamDewarper
    {
    public:
        explicit BaseCamDewarper(int frameW, int frameH) : _frameW(frameW), _frameH(frameH) {}
        BaseCamDewarper():_frameW(0),_frameH(0){}

        virtual  ~BaseCamDewarper() {}

        /**
         * @brief only dewarp and output a subwindow's image
         * @param inFrame [in]  the whole frame
         * @param out   [out]  output dewarped image
         * @param window  [in]  roi window
         * @return
         */
        virtual Status dewarp_with_subwindow(cv::Mat &inFrame,
                                             cv::Mat &out,
                                             cv::Rect window);

        virtual Status init_models(std::string modelPath) {return CZCV_OK;};

    protected:
        int _frameW, _frameH;
    };

    class PUBLIC CPU_CamDewarper_150 : public BaseCamDewarper
    {
    public:
        explicit  CPU_CamDewarper_150(int w, int h)
        {
            _frameW = w;
            _frameH = h;
        }
        CPU_CamDewarper_150() {};

        Status dewarp_with_subwindow(cv::Mat &inFrame, cv::Mat &out, cv::Rect window) override;

        Status init_models(std::string modelPath);
        void find_corrected_rect(cv::Mat& map, cv::Rect rect, cv::Rect& newrect);

    private:
        int _dewarpW;
        int _dewarpH; 
        cv::Mat _mapDtoS;  
        cv::Mat _mapStoD;
        
    };


}//namespace  czcv_camera

#endif //CZCV_CAMERA_CAM_DEWARPER_H
