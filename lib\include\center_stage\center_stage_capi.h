#ifndef CZCV_CAMERA_ANDROID_CAPI_H
#define CZCV_CAMERA_ANDROID_CAPI_H


typedef enum
{
    enTrackModeVideo = 0,  //仅视觉追踪
    enTrackModeVideoandAudio = 1, //视觉+声音追踪
    enTrackModeNo = 2  //无追踪
}enTrackMode;

typedef enum
{
    ModeVideo = 0,  //音频算法未调用
    ModeVideoandAudio = 1, //音频与视频算法同时调用
}ConferenceMode;


//visibility
#if defined _WIN32 || defined __CYGWIN__
#ifdef BUILDING_DLL
#ifdef __GNUC__
#define PUBLIC __attribute__((dllexport))
#else
#define PUBLIC __declspec(dllexport)
#endif
#else
#ifdef __GNUC__
#define PUBLIC __attribute__((dllimport))
#else
#define PUBLIC __declspec(dllimport)
#endif
#endif
#define LOCAL_FUNC
#else
#if __GNUC__ >= 4
#define PUBLIC __attribute__((visibility("default")))
#define LOCAL_FUNC __attribute__((visibility("hidden")))
#else
#define PUBLIC
#define LOCAL_FUNC
#endif
#endif

extern "C"
{
    typedef struct _DoAInfo
    {
        float angle = -1.0f;
    }DoAInfo;
    
    typedef int (*czcv_doa_callback) (DoAInfo* pstDoaInfo);

    typedef enum
    {    
        enGestureEventPersonTrackStart = 0,
        enGestureEventPersonTrackStop = 1, 
        enGestureEventWhiteboardStart = 2,
        enGestureEventWhiteboardStop = 3, 
        enGestureEventMoveZoom = 4,
        enGestureEventMoveLeft = 5,
        enGestureEventMoveRight = 6,
        enGestureEventZoomIn = 7,
        enGestureEventZoomOut = 8,
        enGestureEventLeftBorder = 9, 
        enGestureEventRightBorder = 10,
        enGestureEventZoomInBorder = 11,
        enGestureEventZoomOutBorder = 12,
        enGestureEventWhiteboardSuccess = 13,
        enGestureEventWhiteboardFail = 14, 
        enGestureEventNone = 0xFF,
    }enGestureEvent;

    typedef int (*czcv_gesture_event_callback) (int event);

    typedef enum {
        LED_MODE_OFF,    
        LED_MODE_ON,     
        LED_MODE_BLINK,   
    } LEDMode;

    typedef int (*czcv_camera_led_callback) (int mode);
    /*
    功能：
        算法库初始化
    参数：
        src_width：输入图象宽度
        src_height：输入图象高度
        dst_width：目的图像宽度
        dst_height：目的图像高度
        modelpath：算法模型路径
    返回值：
        算法句柄，NULL：初始化失败，其它：初始化成功

    */
    // PUBLIC void* czcv_camera_init(int src_width, int src_height, int dst_width, int dst_height, 
    //     const char* modelpath, int only_cpu,int external_alg, int gesture_mode, czcv_doa_callback pfun_doa_callback,
    //     czcv_gesture_event_callback pfun_gesture_event_callback, czcv_camera_led_callback pfun_camera_led_callback);
    
    PUBLIC void* czcv_camera_init(int src_width, int src_height, int dst_width, int dst_height, 
        const char* modelpath, int only_cpu,int external_alg, int gesture_mode, czcv_doa_callback pfun_doa_callback,
        czcv_gesture_event_callback pfun_gesture_event_callback);

    /*
    功能：
        人像追踪
    参数：
        handle：算法句柄
        vir_addr：输入图像虚拟内存地址
        phy_addr：输入图像物理内存地址
        width：输入图像宽度
        height：输入图像高度
        dst_vir_addr：输出图像虚拟内存地址
        dst_phy_addr：输出图像物理内存地址
    返回值：
        运行结果，0：成功，<0：失败
    */
    PUBLIC int czcv_camera_run(void* handle, void* vir_addr, int phy_addr, int width, int height, void* dst_vir_addr, int dst_phy_addr, int low_consumption);
    //PUBLIC int czcv_camera_run(void* handle, void* vir_addr, int phy_addr, int width, int height, void* dst_vir_addr, int dst_phy_addr, void** out_vir_addr, int* out_phy_addr);
    /*
    功能：
        算法库去初始化
    参数：
        handle：算法句柄
    返回值：
        运行结果，0：成功，<0：失败
    */
    PUBLIC int czcv_camera_deinit(void* handle);

    /*
    功能：
        设置追踪模式
    参数：
        handle：算法句柄
        mode：追踪模式enTrackMode
    返回值：
        运行结果，0：成功，<0：失败
    */
    PUBLIC int czcv_camera_set_mode(void* handle, int mode);

    /*
    功能：
        设置检测框
    参数：
        handle：算法句柄
        boxinfo：检测框，数据类型为
                typedef struct _PersonBBox
                {
                    int person_x0, person_y0, person_x1, person_y1; // 人物整体的坐标，两点坐标法确定一个框体
                    float person_conf; // 置信度
                }PersonBBox;
        boxnum：检测框个数
    */
    PUBLIC void czcv_camera_set_person_boxes(void* handle, void* boxinfo, int boxnum,void* vir_addr, int width, int height);

    /*
    功能：
        设置取景窗口
    参数：
        handle：算法句柄
        x0：左上角x坐标
        y0：左上角y坐标
        x1：右下角x坐标
        y1：右下角y坐标
    */
    PUBLIC void czcv_camera_get_view_window(void* handle, int* x0, int* y0, int* x1, int* y1);

    PUBLIC void* czcv_camera_get_rga_handle(void* handle);

    PUBLIC int czcv_camera_run_sub(void* handle, void* vir_addr, int phy_addr, int width, int height);
}


#endif