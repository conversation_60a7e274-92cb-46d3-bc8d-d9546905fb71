// Copyright (C) 2021 CZUR Limited. All rights reserved.
// Only CZUR inner use.
// Author: l<PERSON><PERSON><PERSON><PERSON><PERSON>@czur.com

#ifndef CZCV_CAMERA_PERSON_VIEWER_H
#define CZCV_CAMERA_PERSON_VIEWER_H

#include <base/common.h>
#include <base/status.h>
#include <base/mem_allocator.h>
#include <base/bbox.h>
#include <chrono>
#include <thread>
#include <condition_variable>
#include <mutex>
#include "cam_dewarper.h"

namespace czcv_camera
{
    class  Abstarct_PersonViewer_DataCallback
    {
    public:
        Abstarct_PersonViewer_DataCallback(){}
        virtual  ~Abstarct_PersonViewer_DataCallback(){}

        /**
         *
         * @return tell me the returned data you need
         */
        virtual  ImageFormat data_format()
        {
            return  NV21_format;
        }
        /**
         * @brief  we use external managed memory, write frame data into it
         * @param w [in]  frame w
         * @param h [in]  frame h
         * @return mem addr or nullptr
         *
         * @note  ! mem size must match with data_format()
         */
        virtual  void * alloc_frame_mem(int w, int  h) = 0;
        virtual void* alloc_frame_mem(int w, int h, int channel) = 0;
        /**
         * @brief call by alg
         * @param data [out]
         * @param w [out]
         * @param h  [out]
         */
        virtual  void  on_frame_data(void * data, int w, int h) = 0;

        /**
         * @brief call by alg, to notify we will never callback int the future
         */
        virtual  void on_destroy() {}
    };

    /**
     * @brief 阻塞式，一块buf 重用的BGR DataCallback
     *        外部研发可定制不同的DataCallback 自行管理内存
     */
    class PUBLIC Blocked_BGR_PersonViewer_DataCallback: public Abstarct_PersonViewer_DataCallback
    {
    public:
        Blocked_BGR_PersonViewer_DataCallback(int w, int h):_isInitialState(true),_buf(nullptr),_w(w), _h(h)
        {
            _frameReady = false;
        }
        ~Blocked_BGR_PersonViewer_DataCallback()
        {
            if(_buf != nullptr)
            {
                _allocator.fastFree(_buf);
            }
            _buf = nullptr;
        }
        ImageFormat data_format() override
        {
            return BGR_format;
        }
        void * alloc_frame_mem(int w, int h) override
        {
            if(_isInitialState)
            {
                size_t  size = 3 * w * h;
                _buf = _allocator.fastMalloc(size);
				_isInitialState = false;
            }
            //reuse
            return  _buf;
        }

        void* alloc_frame_mem(int w, int h, int channel) override
        {
            if (_isInitialState)
            {
                size_t  size = channel * w * h;
                _buf = _allocator.fastMalloc(size);
                _isInitialState = false;
            }
            //reuse
            return  _buf;
        }
        void on_frame_data(void *data, int w, int h) override
        {
            std::unique_lock<std::mutex> lck(_mtx);
            // actually data is _buf;
            cv::Mat  temp(_h, _w, CV_8UC3, data);
            _frame = temp.clone();
            _frameReady = true;
            _cv.notify_one();
        }
        void on_destroy() override
        {
            //_allocator.fastFree(_buf);
            //_buf = nullptr;
        }
        void pull_frame(cv::Mat &pulled, int waitSecs = 10)
        {
            std::unique_lock<std::mutex> lck(_mtx);
            auto now = std::chrono::system_clock::now();
            if (_cv.wait_until(lck, now + std::chrono::seconds(waitSecs),
                               [&]() {return _frameReady; }))
            {
                pulled = _frame;
                _frameReady = false;
            }
            else
            {
                LOGE("Wait time out!\n");
            }

        }

    private:
        cv::Mat _frame;
        bool _isInitialState;
        UnlockedPoolAllocator _allocator;
        void  * _buf;
        int _w, _h;

        std::condition_variable _cv;
        std::mutex _mtx;
        bool _frameReady;

    };


    typedef struct
    {
        int _instanceId;
        int _inWinIndex;    
    }stInstanceInfo;

    class  PUBLIC Base_PersonViewer
    {
    public:
        virtual  bool  is_gpu() {return  false; }
        virtual ~Base_PersonViewer(){}

        Status bind_dewarper(std::shared_ptr<BaseCamDewarper> & camDewarperPtr);
        /**
         * @brief 绑定数据回调
         * @param dataCallbackPtr
         * @return
         */
        Status bind_callback(std::shared_ptr<Abstarct_PersonViewer_DataCallback> & dataCallbackPtr);

        Status bind_ops_interface(std::shared_ptr<rga_interface_t> &rgaInterfacePtr);

        /**
         * @brief 根据windows 进行拼图操作
         * @param frameIn [in]
         * @param frameOut [out]
         * @param windows [in]
         * @return
         */
        virtual  Status on_process_frame(cv::Mat &frameIn,
                                cv::Mat &frameOut,
                                std::vector<BboxF> &windows, int phy_addr=-1, void* dst_vir_addr=nullptr, int dst_phy_addr=-1);

        Status process_merge(std::vector<BboxF>& rects, std::vector<std::vector<int>>& merged_indeces);
        
        int max_subwin_num() { return _maxSubwinNum; };
        void max_subwin_num(int maxSubwinNum) { _maxSubwinNum = maxSubwinNum; };
        int preview_width() { return _previewwidth; };
        void preview_width(int previewwidth) { _previewwidth = previewwidth; };
        int preview_height() { return _previewheight; };
        void preview_height(int previewheight) { _previewheight = previewheight; };

        int out_width() { return _outwidth; };
        void out_width(int outwidth) { _outwidth = outwidth; };
        int out_height() { return _outheight; };
        void out_height(int outheight) { _outheight = outheight; };

        bool merge() {return _bmerge;};
        void merge(bool bmerge) {_bmerge = bmerge;};

    protected:

        friend  class PersonCenterStagerImpl;
        std::shared_ptr<BaseCamDewarper> _camDewarperPtr = nullptr;
        std::shared_ptr<Abstarct_PersonViewer_DataCallback>  _dataCallbackPtr = nullptr;
        std::shared_ptr<rga_interface_t> _rgaInterfacePtr = nullptr;
    private:
        int _maxSubwinNum = 1;
        std::vector<stInstanceInfo> _infos;
        int _previewwidth;
        int _previewheight;
        const int _MIN_HEIGHT_ = 480;

        int _outwidth = 0;
        int _outheight = 0;

        bool _bmerge = false;
        cv::Mat _mapx;
        cv::Mat _mapy;
        
    };

    /**
     *  GPU  的需要联合考虑dewarp 和 viewer 的图形排列以加快效率
     */
    class GPU_PersonViewer_With_Dewarp: public Base_PersonViewer
    {
    public:
        bool  is_gpu()  override {return  true; }

        /**
         * @param frameIn
         * @param frameOut
         * @param windows
         * @return
         */
        Status on_process_frame(cv::Mat &frameIn, cv::Mat &frameOut, std::vector<BboxF> &windows, int phy_addr=-1, void* dst_vir_addr=nullptr, int dst_phy_addr=-1) override;
    };


}

#endif //CZCV_CAMERA_PERSON_VIEWER_H
