// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_CAMERA_CONFIG_SETTER_H
#define CZCV_CAMERA_CONFIG_SETTER_H

#include <base/dynamic_param.h>
#include <base/status.h>

namespace  czcv_camera
{
    class PUBLIC ConfigSetter
    {
    public:
        /**
         * @brief 敏感度设置,立即生效
         * @param level [in] [0, 2]
         *        0: 低灵敏度
         *        1：中灵敏度
         *        2：高灵敏度
         */
        static void set_sensitivity(int level);
        /*******************************/

        ///// 以下API 用于算法超参数调整， 比如从服务端取出 ////
        ///// 非算法或者测试人员，无需关注具体字段含义 //////
        /**
         * TODO
         * @note 非所有参数立即生效
         * @brief 装载算法内部超参数
         * @param jsonfile [in]  json文件路径
         * @return see @Status
         */
        static Status load_config_from_json_file(std::string  jsonfile);
        /****************************************************/

        /**
         * TODO
         * @note 非所有参数立即生效
         * @brief 装载算法内部超参数
         * @param jsonstr [in] json 字符串
         * @return see @Status
         */
        static Status load_config_from_json_string(const std::string &jsonstr);
        /****************************************************/

        /**
         * @brief for possible upload
         * @return
         */
        static std::string dump_inner_params_to_json_str(const std::string  dumpToFile="algo_params.json");
        /***************************************************/

        static DynamicParams _hyperParams;
    };
}
#endif //CZCV_CAMERA_CONFIG_SETTER_H
