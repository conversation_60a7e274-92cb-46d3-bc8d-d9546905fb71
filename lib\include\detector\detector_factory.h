// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_CAMERA_DETECTOR_FACTORY_H
#define CZCV_CAMERA_DETECTOR_FACTORY_H

#include <string>
#include <map>
#include "base/status.h"
#include "detector/base_detector.h"
#include "detector/detector_id.h"

namespace czcv_camera
{
    class PUBLIC AbstractDetectorCreator
    {
    public:
        virtual BaseObjectDetector *create_detector(DetectorID id) = 0;
        virtual ~AbstractDetectorCreator(){};
    };

    template <typename T>
    class PUBLIC TempalteDetectorCreator:public AbstractDetectorCreator
    {
        BaseObjectDetector *create_detector(DetectorID id)
        {
            return new T();
        }
    };

    PUBLIC std::map<DetectorID, std::shared_ptr<AbstractDetectorCreator> >& get_detector_creator_map();
    PUBLIC Status  register_detector(DetectorID id, AbstractDetectorCreator *creator);

    template <typename T>
    class PUBLIC DetectorRegister {
    public:
        explicit DetectorRegister(DetectorID id)
        {
            register_detector(id, new T());
        }
    };

// example: DECLARE_DETECTOR(MobileV3_Head)
#define DECLARE_DETECTOR(type_string)\
    class CZCV_##type_string##Detector : public     BaseObjectDetector             \
    {                                                                              \
    public:                                                                        \
        virtual ~CZCV_##type_string##Detector();                                   \
        virtual Status init(std::vector<std::string> modelConfig) override;        \
        virtual Status on_set_arg() override;                                      \
        virtual Status sub_run(DetInputOutput &inputOutput) override;              \
        virtual Status init(std::vector<std::string> modelConfig,std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType) override;              \
        virtual Status release() override; \
    }

#define REGISTER_DETECTOR(type_string, id)\
    static DetectorRegister<TempalteDetectorCreator<CZCV_##type_string##Detector>>\
        g_czcv_##type_string##_detector_register(id);

    /**
     * @brief create a detector by DetectorID
     * @param id [in] see DetectorID
     * @return  see BaseObjectDetector
     */
    PUBLIC std::shared_ptr<BaseObjectDetector> create_detector(DetectorID id);
    /****************************************************************/
}
#endif //CZCV_CAMERA_DETECTOR_FACTORY_H
