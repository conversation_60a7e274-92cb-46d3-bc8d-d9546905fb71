// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_CAMERA_TRACKER_FACTORY_H
#define CZCV_CAMERA_TRACKER_FACTORY_H

#include "base/status.h"
#include "tracker/base_tracker.h"
#include "tracker/tracker_id.h"

namespace czcv_camera
{
    class PUBLIC AbstractTrackerCreator
    {
    public:
        virtual BaseTracker *create_tracker(TrackerID id) = 0;
        virtual ~AbstractTrackerCreator(){};
    };

    template <typename T>
    class PUBLIC TempalteTrackerCreator:public AbstractTrackerCreator
    {
        BaseTracker *create_tracker(TrackerID id)
        {
            return new T();
        }
    };

    PUBLIC std::map<TrackerID, std::shared_ptr<AbstractTrackerCreator> >& get_tracker_creator_map();
    PUBLIC Status  register_tracker(TrackerID id, AbstractTrackerCreator *creator);

    template <typename T>
    class PUBLIC TrackerRegister {
    public:
        explicit TrackerRegister(TrackerID id)
        {
            register_tracker(id, new T());
        }
    };

// example: DECLARE_Tracker(ONet)
#define DECLARE_Tracker(type_string)\
    class CZCV_##type_string##Tracker : public     BaseTracker             \
    {                                                                              \
    public:                                                                        \
        virtual ~CZCV_##type_string##Tracker();                                   \
        virtual Status init(std::vector<std::string> modelConfig) override;        \
        virtual Status on_set_arg() override;                                      \
        virtual Status sub_run(TrackerInputOutput &inputOutput) override;              \
    }

#define REGISTER_TRACKER(type_string, id)\
    static TrackerRegister<TempalteTrackerCreator<CZCV_##type_string##Tracker>>\
        g_czcv_##type_string##_tracker_register(id);

    /**
     * @brief create a Tracker by TrackerID
     * @param id [in] see TrackerID
     * @return  see BaseTracker
     */
    PUBLIC std::shared_ptr<BaseTracker> create_tracker(TrackerID id);
    /****************************************************************/
}


#endif //CZCV_CAMERA_TRACKER_FACTORY_H
