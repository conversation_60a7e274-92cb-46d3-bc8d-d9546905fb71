// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include "base/abstract_model.h"

namespace czcv_camera
{
    /**
     * @brief init model with config
     * @param modelConfig [in] model or config path
     * @return see Status,CZCV_OK if success
     */
    Status AbstarctModel::init(std::vector<std::string> modelConfig)
    {
        _modelConfig = modelConfig;
        return CZCV_OK;
    }

    Status AbstarctModel::init(std::vector<std::string> modelConfig,std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType)
    {
        _modelConfig = modelConfig;
        _modelType = modelType;
        return CZCV_OK;
    }

    Status AbstarctModel::release()
    {
        return CZCV_OK;
    }
    /***************************************************/
}//namespace czcv_mobile

