// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include "base/common.h"

namespace czcv_camera
{


    ImageBlob::ImageBlob()
    {
        buffer = nullptr;
        height = 0;
        width  = 0;
        stride = 0;
        index  = 0;
        imgformat = RGB_format;
        frameCounter = 0;
    }
    ImageBlob::ImageBlob(const ImageBlob &b)
    {
        if(b.frameCopiedBGR.data)
            frameCopiedBGR = b.frameCopiedBGR.clone();
        buffer = b.buffer;
        height = b.height;
        width  = b.width;
        stride = b.stride;
        index  = b.index;
        imgformat = b.imgformat;
        timestamp = b.timestamp;
        frameCounter = b.frameCounter;
    }
    ImageBlob & ImageBlob::operator=(const ImageBlob &b)
    {
        if(b.frameCopiedBGR.data)
            frameCopiedBGR = b.frameCopiedBGR.clone();
        buffer = b.buffer;
        height = b.height;
        width  = b.width;
        stride = b.stride;
        index  = b.index;
        imgformat = b.imgformat;
        timestamp = b.timestamp;
        frameCounter = b.frameCounter;
        return *this;
    }


    void nv21resize(cv::Mat& nv21, cv::Mat& dstnv21, cv::Rect& rect, cv::Rect& dstrect)
    {
        int rows = nv21.rows * 2 / 3;
        int cols = nv21.cols;

        int dstwidth = dstnv21.cols;
        int dstheight = dstnv21.rows * 2 / 3;

        if (rect.width == 0 || rect.height == 0)
        {
            rect = cv::Rect(0, 0, cols, rows);
        }
        if (dstrect.width == 0 || dstrect.height == 0)
        {
            dstrect = cv::Rect(0, 0, dstwidth, dstheight);
        }
    
        cv::Mat y(rows, cols, CV_8UC1, nv21.data);
        cv::Mat vu(rows / 2, cols / 2, CV_8UC2, ((unsigned char*)nv21.data + rows * cols));

        cv::Mat dsty(dstheight, dstwidth, CV_8UC1, dstnv21.data);
        cv::Mat dstvu(dstheight / 2, dstwidth / 2, CV_8UC2, (unsigned char*)dstnv21.data + dstwidth * dstheight);

        cv::resize(y(rect), dsty(dstrect), cv::Size(dstrect.width, dstrect.height), 0, 0, cv::INTER_LINEAR);
        cv::Rect vurect(rect.x / 2, rect.y / 2, rect.width / 2, rect.height / 2);
        cv::Rect dstvurect(dstrect.x / 2, dstrect.y / 2, dstrect.width / 2, dstrect.height / 2);
        cv::resize(vu(vurect), dstvu(dstvurect), cv::Size(dstvurect.width, dstvurect.height), 0, 0, cv::INTER_NEAREST);
    }

    void nv21remap(cv::Mat& nv21, cv::Mat& dstnv21, cv::Mat& mapx, cv::Mat& mapy)
    {
        int rows = nv21.rows * 2 / 3;
        int cols = nv21.cols;

        int dstwidth = dstnv21.cols;
        int dstheight = dstnv21.rows * 2 / 3;

        cv::Mat y(rows, cols, CV_8UC1, nv21.data);
        cv::Mat vu(rows / 2, cols / 2, CV_8UC2, ((unsigned char*)nv21.data + rows * cols));

        cv::Mat dsty(dstheight, dstwidth, CV_8UC1, dstnv21.data);
        cv::Mat dstvu(dstheight / 2, dstwidth / 2, CV_8UC2, (unsigned char*)dstnv21.data + dstwidth * dstheight);
        
        cv::remap(y, dsty, mapx, mapy, cv::INTER_NEAREST, cv::BORDER_REPLICATE);
        
        cv::Mat mapxvu;
        cv::Mat mapyvu;
        cv::resize(mapx, mapxvu, cv::Size(mapx.cols / 2, mapx.rows / 2), 0.0, 0.0, cv::INTER_NEAREST);
        cv::resize(mapy, mapyvu, cv::Size(mapy.cols / 2, mapy.rows / 2), 0.0, 0.0, cv::INTER_NEAREST);
        mapxvu = mapxvu *0.5f;
        mapyvu = mapyvu *0.5f;
        cv::remap(vu, dstvu, mapxvu, mapyvu, cv::INTER_NEAREST, cv::BORDER_REPLICATE);
    }

}//namespace czcv_camera