// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include "base/mem_allocator.h"

#if __ANDROID_API__ >= 26
#include <android/hardware_buffer.h>
#endif // __ANDROID_API__ >= 26

namespace czcv_camera
{

    Allocator::~Allocator()
    {
    }

    PoolAllocator::PoolAllocator()
    {
        size_compare_ratio = 192; // 0.75f * 256
    }

    PoolAllocator::~PoolAllocator()
    {
        clear();

        if (!payouts.empty())
        {
            LOGE("FATAL ERROR! pool allocator destroyed too early");
        }
    }

    void PoolAllocator::clear()
    {
        budgets_lock.lock();

        std::list<std::pair<size_t, void*> >::iterator it = budgets.begin();
        for (; it != budgets.end(); ++it)
        {
            void* ptr = it->second;
            czcv_camera::fastFree(ptr);
        }
        budgets.clear();
        budgets_lock.unlock();
    }

    void PoolAllocator::set_size_compare_ratio(float scr)
    {
        if (scr < 0.f || scr > 1.f)
        {
            LOGE("invalid size compare ratio %f", scr);
            return;
        }
        size_compare_ratio = (unsigned int)(scr * 256);
    }

    void* PoolAllocator::fastMalloc(size_t size)
    {
        budgets_lock.lock();

        // find free budget
        std::list<std::pair<size_t, void*> >::iterator it = budgets.begin();
        for (; it != budgets.end(); ++it)
        {
            size_t bs = it->first;

            // size_compare_ratio ~ 100%
            if (bs >= size && ((bs * size_compare_ratio) >> 8) <= size)
            {
                void* ptr = it->second;
                budgets.erase(it);
                budgets_lock.unlock();
                payouts_lock.lock();
                payouts.push_back(std::make_pair(bs, ptr));
                payouts_lock.unlock();
                return ptr;
            }
        }
        budgets_lock.unlock();

        // new
        void* ptr = czcv_camera::fastMalloc(size);
        payouts_lock.lock();
        payouts.push_back(std::make_pair(size, ptr));
        payouts_lock.unlock();

        return ptr;
    }

    void PoolAllocator::fastFree(void* ptr)
    {
        payouts_lock.lock();
        // return to budgets
        std::list<std::pair<size_t, void*> >::iterator it = payouts.begin();
        for (; it != payouts.end(); ++it)
        {
            if (it->second == ptr)
            {
                size_t size = it->first;
                payouts.erase(it);
                payouts_lock.unlock();
                budgets_lock.lock();
                budgets.push_back(std::make_pair(size, ptr));
                budgets_lock.unlock();
                return;
            }
        }

        payouts_lock.unlock();

        LOGE("FATAL ERROR! pool allocator get wild %p", ptr);
        czcv_camera::fastFree(ptr);
    }

    UnlockedPoolAllocator::UnlockedPoolAllocator()
    {
        size_compare_ratio = 192; // 0.75f * 256
    }

    UnlockedPoolAllocator::~UnlockedPoolAllocator()
    {
        clear();

        if (!payouts.empty())
        {
            LOGE("FATAL ERROR! unlocked pool allocator destroyed too early");
        }
    }

    void UnlockedPoolAllocator::clear()
    {
        std::list<std::pair<size_t, void*> >::iterator it = budgets.begin();
        for (; it != budgets.end(); ++it)
        {
            void* ptr = it->second;
            czcv_camera::fastFree(ptr);
        }
        budgets.clear();
    }

    void UnlockedPoolAllocator::set_size_compare_ratio(float scr)
    {
        if (scr < 0.f || scr > 1.f)
        {
            LOGE("invalid size compare ratio %f", scr);
            return;
        }
        size_compare_ratio = (unsigned int)(scr * 256);
    }

    void* UnlockedPoolAllocator::fastMalloc(size_t size)
    {
        // find free budget
        std::list<std::pair<size_t, void*> >::iterator it = budgets.begin();
        for (; it != budgets.end(); ++it)
        {
            size_t bs = it->first;

            // size_compare_ratio ~ 100%
            if (bs >= size && ((bs * size_compare_ratio) >> 8) <= size)
            {
                void* ptr = it->second;
                budgets.erase(it);
                payouts.push_back(std::make_pair(bs, ptr));
                return ptr;
            }
        }
        // new
        void* ptr = czcv_camera::fastMalloc(size);
        payouts.push_back(std::make_pair(size, ptr));

        return ptr;
    }

    void UnlockedPoolAllocator::fastFree(void* ptr)
    {
        // return to budgets
        std::list<std::pair<size_t, void*> >::iterator it = payouts.begin();
        for (; it != payouts.end(); ++it)
        {
            if (it->second == ptr)
            {
                size_t size = it->first;

                payouts.erase(it);

                budgets.push_back(std::make_pair(size, ptr));

                return;
            }
        }
        LOGE("FATAL ERROR! unlocked pool allocator get wild %p", ptr);
        czcv_camera::fastFree(ptr);
    }

}//namespace czcv_mobile

