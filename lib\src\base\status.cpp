// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include "base/status.h"
#include <iostream>
#include <iomanip>
#include <sstream>

namespace czcv_camera
{
    Status::~Status()
    {
        //code_    = 0;
        //message_ = "";
    }

    Status::Status(int code, std::string message)
    {
        code_    = code;
        message_ = message;
    }

    Status& Status::operator=(int code)
    {
        code_    = code;
        message_ = "";
        return *this;
    }

    bool Status::operator==(int code)
    {
        return code_ == code;
    }

    bool Status::operator!=(int code)
    {
        return code_ != code;
    }

    Status::operator int()
    {
        return code_;
    }

    Status::operator bool()
    {
        return code_ == CZCV_OK;
    }

    std::string Status::description()
    {
        std::ostringstream os;
        os << "code: 0x" << std::uppercase << std::setfill('0') << std::setw(4) << std::hex << code_
           << " msg: " << message_;
        return os.str();
    }

}//namespace czcv_mobile