
FILE(GLOB SRCS
        ../base/abstract_model.cpp
        ../base/macro.cpp
        ../base/nms.cpp
        ../base/status.cpp
        ../base/common.cpp
        ../base/mem_allocator.cpp
        ../config/config_setter.cpp

        ../detector/base_detector.cpp
        ../detector/yolox_person_det.cpp
        ../detector/center_crop_demo_det.cpp
        ../detector/detector_factory.cpp

        ../detector/detail/tnn_yolox.cpp

        ../tracker/*.cpp
        ../tracker/detail/*.cpp

        ../utils/async_runner.cpp
        ../utils/img_cvt_utils.cpp
        ../utils/lapjv.cpp

        cam_dewarper.cpp
        czcv_center_stage.cpp
        person_viewer.cpp)

message(${SRCS})
#if(WIN32)
#    list(APPEND SRCS ../rc/czcv_mobile_intelligence.rc)
#endif()
if(BUILD_Shared)
    add_library(czcv_mobile_meeting_alg SHARED  ${SRCS})
else()
    add_library(czcv_mobile_meeting_alg STATIC  ${SRCS} )
endif()

if(APPLE)
    target_link_libraries(czcv_mobile_meeting_alg   ${TNN_LIBS}  ${OpenCV_LIBS}  ${GLOG_LIBS})
else()
    target_link_libraries(czcv_mobile_meeting_alg  -Wl,--whole-archive ${TNN_LIBS} -Wl,--no-whole-archive  ${OpenCV_LIBS}  ${GLOG_LIBS})
endif()