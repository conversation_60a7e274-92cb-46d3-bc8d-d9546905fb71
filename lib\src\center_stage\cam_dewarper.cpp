// Copyright (C) 2021 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include "center_stage/cam_dewarper.h"


namespace czcv_camera
{
    Status BaseCamDewarper::dewarp_with_subwindow(cv::Mat &inFrame, cv::Mat &out, cv::Rect window)
    {
        out = inFrame(window).clone();
        //cv::resize(roi, out, inFrame.size());
        return  CZCV_OK;
    }

    Status CPU_CamDewarper_150::dewarp_with_subwindow(cv::Mat &inFrame, cv::Mat &out, cv::Rect window) 
    {
        cv::Rect dewarpRect;
        find_corrected_rect(_mapStoD, window, dewarpRect);

		cv::remap(inFrame, out, _mapDtoS(dewarpRect), cv::Mat(), cv::INTER_LINEAR);

        return CZCV_OK;
    }

    Status CPU_CamDewarper_150::init_models(std::string modelPath)
    {
        FILE* f = fopen(modelPath.c_str(), "rb");
        if (nullptr == f)
        {
            return CZCV_PARAM_ERR;
        }

        size_t size = fread(&_frameW, sizeof(int), 1, f);
        size += fread(&_frameH, sizeof(int), 1, f);
        size += fread(&_dewarpW, sizeof(int), 1, f);
        size += fread(&_dewarpH, sizeof(int), 1, f);

        _mapDtoS = cv::Mat::zeros(_dewarpH, _dewarpW, CV_32FC2);
        _mapStoD = cv::Mat::zeros(_frameH, _frameW, CV_32SC2);

        size += fread(_mapDtoS.data, sizeof(float), _dewarpH * _dewarpW * 2, f);
        size += fread(_mapStoD.data, sizeof(int), _frameH * _frameW * 2, f);

        if ((int)size != 4 + _dewarpH * _dewarpW * 2 + _frameH * _frameW * 2)
        {
            fclose(f);
            return CZCV_PARAM_ERR;
        }
        
        fclose(f);
        return CZCV_OK;
    }

    
    void CPU_CamDewarper_150::find_corrected_rect(cv::Mat& map, cv::Rect rect, cv::Rect& newrect)
    {
        int left = _dewarpW;
        int up = _dewarpH;
        int right = -1;
        int bottom = -1;

        bool bfindl = false;
        bool bfindr = false;
        for (int j = 0; j < rect.width; j++)
        {
            for (int i = rect.y; i < rect.y + rect.height; i++)
            {
                if (map.ptr<int>(i, j + rect.x)[0] < left && map.ptr<int>(i, j + rect.x)[0] > -0.5)
                {
                    left = map.ptr<int>(i, j + rect.x)[0];
                }
                if (map.ptr<int>(i, rect.x + rect.width - j)[0] > right && map.ptr<int>(i, rect.x + rect.width - j)[0] > -0.5)
                {
                    right = map.ptr<int>(i, rect.x + rect.width - j)[0];
                }

                if (left != _dewarpW)
                {
                    bfindl = true;
                }
                if (right != -1)
                {
                    bfindr = true;
                }
            }

            if (bfindl && bfindr)
            {
                break;
            }
        }

        bool bfindu = false;
        bool bfindb = false;
        for (int j = 0; j < rect.height; j++)
        {
            for (int i = rect.x; i < rect.x + rect.width; i++)
            {
                if (map.ptr<int>(j + rect.y, i)[1] < up && map.ptr<int>(j + rect.y, i)[1] > -0.5f)
                {
                    up = map.ptr<int>(j + +rect.y, i)[1];
                }
                if (map.ptr<int>(rect.y + rect.height - j, i)[1] > bottom &&
                    map.ptr<int>(rect.y + rect.height - j, i)[1] > -0.5)
                {
                    bottom = map.ptr<int>(rect.y + rect.height - j, i)[1];
                }

                if (up != _dewarpH)
                {
                    bfindu = true;
                }
                if (bottom != -1)
                {
                    bfindb = true;
                }
            }

            if (bfindu && bfindb)
            {
                break;
            }
        }

        newrect.x = left;
        newrect.y = up;
        newrect.width = std::max(0, right - left);
        newrect.height = std::max(0, bottom - up);
    }

    
}


