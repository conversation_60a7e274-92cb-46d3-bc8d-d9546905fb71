#include "center_stage/center_stage_api.h"

extern "C"
{
    // timeval init_time={0,0};
    // int bnotrack = false;
    // int bvisiontrack = false;
    PUBLIC void* czcv_camera_init(int src_width, int src_height, int dst_width, int dst_height, const char* modelpath, int only_cpu,int external_alg, int gesture_mode, 
                        czcv_doa_callback pfun_doa_callback, czcv_gesture_event_callback pfun_gesture_event_callback)
    //PUBLIC void* czcv_camera_init(int src_width, int src_height, int dst_width, int dst_height, const char* modelpath, int only_cpu,int external_alg, czcv_doa_callback pfun_doa_callback, czcv_camera_led_callback pfun_camera_led_callback)
    {   
        LOGD("czcv_camera_init\n");
        //czcv_gesture_event_callback pfun_gesture_event_callback = nullptr;
        czcv_camera_led_callback pfun_camera_led_callback = nullptr;
        czcv_camera::Android_API* cameraAlg = new czcv_camera::Android_API();
        int ret = cameraAlg->init_api(modelpath, src_width, src_height, dst_width, dst_height, true, only_cpu,external_alg, gesture_mode, pfun_doa_callback, pfun_gesture_event_callback, pfun_camera_led_callback);
        if (ret != 0)
        {
            LOGE("init_api failed\n");
            return NULL;
        }

        return static_cast<void*>(cameraAlg);
    }

    //PUBLIC int czcv_camera_run(void* handle, void* vir_addr, int phy_addr, int width, int height, void* dst_vir_addr, int dst_phy_addr, void** out_vir_addr, int* out_phy_addr)
    PUBLIC int czcv_camera_run(void* handle, void* vir_addr, int phy_addr, int width, int height, void* dst_vir_addr, int dst_phy_addr, int low_consumption)
    {
        int Cmode=1;

        czcv_camera::Android_API* cameraAlg = static_cast<czcv_camera::Android_API*>(handle);

        // *out_vir_addr = dst_vir_addr;
        // *out_phy_addr = dst_phy_addr;
    
        cv::Mat frame(height * 3 / 2, (int)width, CV_8UC1, vir_addr);

        int ret = cameraAlg->run_api(frame, phy_addr, dst_vir_addr, dst_phy_addr, Cmode,low_consumption);
        if (ret != 0)
        {
            LOGE("run_api failed\n");
        }

        return ret;
    }

    PUBLIC int czcv_camera_set_mode(void* handle, int mode)
    {
        //mode = 1;
        czcv_camera::Android_API* cameraAlg = static_cast<czcv_camera::Android_API*>(handle);
        LOGE("czcv_camera_set_mode:%d\n", mode);
        cameraAlg->set_mode((enTrackMode)mode);
        return 0;
    }

    PUBLIC int czcv_camera_deinit(void* handle)
    {
        if (nullptr != handle)
        {
            LOGD("czcv_camera_deinit\n");
            czcv_camera::Android_API* cameraAlg = static_cast<czcv_camera::Android_API*>(handle);
            delete cameraAlg;
        }       

        return 0;
    }

    PUBLIC void czcv_camera_set_person_boxes(void* handle, void* boxinfo, int boxnum,void* vir_addr, int width, int height)
    {
        if (nullptr != handle)
        {
            //LOGD("czcv_camera_set_person_boxes\n");
            czcv_camera::Android_API* cameraAlg = static_cast<czcv_camera::Android_API*>(handle);
            cameraAlg->set_person_boxes(boxinfo, boxnum,vir_addr, width, height);
        } 
    }

    PUBLIC void czcv_camera_get_view_window(void* handle, int* x0, int* y0, int* x1, int* y1)
    {
        if (nullptr != handle)
        {
            //LOGD("czcv_camera_set_person_boxes\n");
            czcv_camera::Android_API* cameraAlg = static_cast<czcv_camera::Android_API*>(handle);
            cameraAlg->get_view_window(x0, y0, x1, y1);
        }  
    }

    PUBLIC int czcv_camera_run_sub(void* handle, void* vir_addr, int phy_addr, int width, int height)
    {
        czcv_camera::Android_API* cameraAlg = static_cast<czcv_camera::Android_API*>(handle);    
        cv::Mat frame(height * 3 / 2, (int)width, CV_8UC1, vir_addr);

        int ret = cameraAlg->run_api_sub(frame, phy_addr);
        if (ret != 0)
        {
            LOGE("run_api_sub failed\n");
        }

        return ret;
    }

    PUBLIC void* czcv_camera_get_rga_handle(void* handle)
    {
        czcv_camera::Android_API* cameraAlg = static_cast<czcv_camera::Android_API*>(handle);
        return cameraAlg->get_rga_handle();
    }
    
}
