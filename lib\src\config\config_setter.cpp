// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include <config/config_setter.h>

#ifdef JSON_SUPPORT
#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#endif
#include <fstream>
#include <sstream>

namespace czcv_camera
{
    DynamicParams ConfigSetter::_hyperParams;

    /**
     * @brief 敏感度设置,立即生效
     * @param level [in] [0, 2]
     *        0: 低灵敏度
     *        1：中灵敏度
     *        2：高灵敏度
     */
    void ConfigSetter::set_sensitivity(int level)
    {
        level = level<0?0:level;
        level = level>2?2:level;
        ConfigSetter::_hyperParams.set("_pose_sense_level_int_", level);
    }
    /*******************************/

#ifdef JSON_SUPPORT
    static  std::string judge_type(std::string key)
    {
        if(key.find("_float_")!= key.npos)
        {
            return "float";
        }
        if(key.find("_bool_")!= key.npos)
        {
            return "bool";
        }
        if(key.find("_int_")!= key.npos)
        {
            return "int";
        }
        return "other";
    }

    Status ConfigSetter::load_config_from_json_file(std::string jsonfile)
    {
        std::ifstream  f(jsonfile.c_str());
        std::string jsonstr((std::istreambuf_iterator<char>(f)),
                        std::istreambuf_iterator<char>());
        if(jsonstr.empty())
        {
            LOGE("load %s json file err!", jsonfile.c_str());
            return false;
        }
        return load_config_from_json_string(jsonstr);
    }
    Status ConfigSetter::load_config_from_json_string(const std::string &jsonstr)
    {
        rapidjson::Document d;
        if(d.Parse(jsonstr.c_str()).HasParseError())
        {
            LOGE("parse json err!");
            return CZCV_PARSE_ERR;
        }
        if(!d.IsObject())
        {
            LOGE("parse json err!");
            return CZCV_PARSE_ERR;
        }
        for(auto iter = d.MemberBegin(); iter != d.MemberEnd(); ++iter)
        {
            rapidjson::Value jKey;
			rapidjson::Value jValue;
			rapidjson::Document::AllocatorType allocator;
			jKey.CopyFrom(iter->name, allocator);
			jValue.CopyFrom(iter->value, allocator);
            if (jKey.IsString())
            {
                auto  key = jKey.GetString();
                if(jValue.IsInt())
                {
                    int v = jValue.GetInt();
                    ConfigSetter::_hyperParams.set(key, v);
                    std::stringstream ss;
                    ss<<key<<"--> int: "<<v<<std::endl;
                    LOGI("%s", ss.str().c_str());
                }
                else if(jValue.IsFloat())
                {
                    float v = jValue.GetFloat();
                    ConfigSetter::_hyperParams.set(key, v);
                    std::stringstream ss;
                    ss<<key<<"--> float: "<<v<<std::endl;
                    LOGI("%s", ss.str().c_str());
                }
                else if(jValue.IsBool())
                {
                    bool v = jValue.GetBool();
                    ConfigSetter::_hyperParams.set(key, v);
                    std::stringstream ss;
                    ss<<key<<"--> bool: "<<v<<std::endl;
                    LOGI("%s", ss.str().c_str());
                }
                else if(jValue.IsString())
                {
                    std::string v = jValue.GetString();
                    ConfigSetter::_hyperParams.set(key, v);
                    std::stringstream ss;
                    ss<<key<<"--> string: "<<v<<std::endl;
                    LOGI("%s", ss.str().c_str());
                }
                
            }
        }
        return CZCV_OK;
    }
    

    /**
     * @brief for possible upload
     * @return
     */
    std::string ConfigSetter::dump_inner_params_to_json_str(const std::string dumpToFile)
    {
        std::vector<std::string> keys = ConfigSetter::_hyperParams.keys();
        rapidjson::Document document;
	    document.SetObject();
	    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
        
        for(auto &k : keys)
        {
            
            std::string type = judge_type(k);
            if(type == "float")
            {
                float v = ConfigSetter::_hyperParams.get(k, 0.0f).AnyCast<float>();
                document.AddMember(rapidjson::StringRef(k.c_str()), v, allocator);
            }
            else if (type == "bool")
            {
                bool v = ConfigSetter::_hyperParams.get(k, true).AnyCast<bool>();
                document.AddMember(rapidjson::StringRef(k.c_str()), v, allocator);
            }
            else if(type == "int")
            {
                int v = ConfigSetter::_hyperParams.get(k, 0).AnyCast<int>();
                document.AddMember(rapidjson::StringRef(k.c_str()), v, allocator);
            }
            
        }
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        document.Accept(writer);
        std::string json = std::string(buffer.GetString());
        if(dumpToFile!="")
        {
            std::ofstream of(dumpToFile, std::ios::out);
            if (of.is_open()) 
            {
                of.write(json.c_str(), json.size());
                of.close();
            }
        }
        return json;
    }
    /***************************************************/
#endif

}//namespace czcv_camera


