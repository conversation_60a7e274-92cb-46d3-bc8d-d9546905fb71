// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_MOBILE_INTELLIGENCE_RKNN_YOLOX_H
#define CZCV_MOBILE_INTELLIGENCE_RKNN_YOLOX_H

#include <vector>
#include <string>
#include <fstream>

#include <opencv2/opencv.hpp>
#include <base/common.h>
#include <base/status.h>
#include <base/abstract_model.h>
#include <detector/base_detector.h>
#include <detector/yolox_person_det.h>
#include "rknn_api.h"



namespace czcv_camera
{
    class YoloxRKNN: public AbstarctModel
    {
    public:

		Status release() 
		{
			rknn_destroy_mem(ctx, input_mems[0]);
			rknn_destroy_mem(ctx, output_mems[0]);
					
			rknn_destroy(ctx);

			if (rgamat_det_pad.handle != NULL)
			{
				int ret;
				ret = _rgaInterfacePtr->free(rgamat_det_pad.handle);
				if (ret != 0)
				{
					LOGE("detect release fail!");
					return CZCV_MEM_ERR;
				}
				else
				{
					LOGE("detect release success!");
					rgamat_det_pad.handle = nullptr;
					return CZCV_OK;
				}	
			}
			return CZCV_OK;
		}
		
        Status run(DetInputOutput &inputOutput);
        Status init(std::vector<std::string> modelConfig,std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType) override;
		
        void  conf_thres(float v){_confThres = v;}
        float conf_thres() const{return  _confThres;}

        void  nms_thres(float v){_nmsThres   = v;}
        float nms_thres() const {return  _nmsThres;}

		void set_primary(bool v) {_is_primary = v;}
		bool is_primary() const {return _is_primary;}

        // void input_size(int v){_inputSize=v;}
        // int  input_size() const{return  _inputSize;}

    private:
		void generate_grids_and_stride(const int target_size,
										std::vector<int>& strides, std::vector<GridAndStride>& grid_strides);
		void generate_yolox_proposals(std::vector<GridAndStride> grid_strides, 
									float * pred_mat, int num_grid, int num_class, float conf_thre,std::vector<Object>& proposals);
		void qsort_descent_inplace(std::vector<Object>& objects);
		void qsort_descent_inplace(std::vector<Object>& objects, int left, int right);
		inline float intersection_area(const Object& a, const Object& b);
		void nms_sorted_bboxes(const std::vector<Object>& objects, std::vector<int>& picked, float nms_threshold);
		std::string fdLoadFile(std::string path);
        float _confThres = 0.2;
        float _nmsThres = 0.4;
        int _inputChannel;
		int _inputWidth;
		int _inputHeight;
		rknn_input_output_num io_num;
		std::vector<rknn_tensor_attr> input_attrs;
		std::vector<rknn_tensor_attr> output_attrs;
		rknn_context ctx;
		rknn_tensor_mem* input_mems[1];
		rknn_tensor_mem* output_mems[1];
		czcv_camera::RgaMat rgamat_det_pad;
		std::shared_ptr<rga_interface_t> _rgaInterfacePtr = nullptr;
		bool _is_primary = false;
    };
}

#endif //CZCV_MOBILE_INTELLIGENCE_RKNN_YOLOX_H
