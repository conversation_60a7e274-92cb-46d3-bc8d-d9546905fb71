// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include <map>
#include "detector/detector_factory.h"

namespace czcv_camera
{
    PUBLIC std::map<DetectorID, std::shared_ptr<AbstractDetectorCreator> >& get_detector_creator_map()
    {
        static std::map<DetectorID, std::shared_ptr<AbstractDetectorCreator> > g_detector_creator_map;
        return g_detector_creator_map;
    }
    PUBLIC Status  register_detector(DetectorID id, AbstractDetectorCreator *creator)
    {
        get_detector_creator_map()[id] = std::shared_ptr<AbstractDetectorCreator>(creator);
        return CZCV_OK;
    }
    /**
     * @brief create a detector by DetectorID
     * @param id [in] see DetectorID
     * @return  see BaseObjectDetector
     */
    PUBLIC std::shared_ptr<BaseObjectDetector> create_detector(DetectorID id)
    {
        auto& m = get_detector_creator_map();
        if( m.find(id) != m.end())
        {
            return std::shared_ptr<BaseObjectDetector>(m[id]->create_detector(id));
        }
        else
        {
            LOGE("create_detector ERR! no such id: %d\n",(int)id);
            return nullptr;
        }
    }
}//namespace czcv
