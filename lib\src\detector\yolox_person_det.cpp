// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.
#include "detail/tnn_yolox.h"
#include "detail/rknn_yolox.h"
#include "yolox_person_det.h"

namespace czcv_camera
{
    CZCV_Yolox_PersonDetector::~CZCV_Yolox_PersonDetector()
    {
        if(_modelPtr!= nullptr)
        {
            delete _modelPtr;   
            _modelPtr = nullptr;
        }
    }

    Status CZCV_Yolox_PersonDetector::init(std::vector<std::string> modelConfig)
    {
        _modelName = "CZCV_YOLOX_PersonDetector";
        Status s = CZCV_OK;
        YoloxTNN * modelPtr = new YoloxTNN();  
        std::shared_ptr<rga_interface_t> rgaInterfacePtr = nullptr;

		s = modelPtr->init(modelConfig, rgaInterfacePtr, EN_MODEL_TYPE_CPU);          
        if (s == CZCV_OK)
        {
            _modelPtr = dynamic_cast<czcv_camera::AbstarctModel *>(modelPtr);
        }   
        else
        {
            delete modelPtr;
        }  
        return  s;
    }

    Status CZCV_Yolox_PersonDetector::init(std::vector<std::string> modelConfig,std::shared_ptr<rga_interface_t> &rgaInterfacePtr, czcv_model_type_t modelType)
    {
        _modelName = "CZCV_YOLOX_PersonDetector";
        _modelType = modelType;
    
        Status s = CZCV_OK;
        if (_modelType == EN_MODEL_TYPE_NPU)
        {
            _modelPtr = new YoloxRKNN();
            reinterpret_cast<czcv_camera::YoloxRKNN*>(_modelPtr)->set_primary(_isPrimary);                
        }  
        else
        {
            _modelPtr = new YoloxTNN();
        }    

        s = _modelPtr->init(modelConfig,rgaInterfacePtr,modelType);  
        if (s != CZCV_OK)
        {
            delete _modelPtr;
        }  
       
        return  s;
    }

    Status CZCV_Yolox_PersonDetector::release()
    {
        Status s;
        if (_modelType == EN_MODEL_TYPE_NPU)
        {
            YoloxRKNN * modelPtr = dynamic_cast<YoloxRKNN *>(_modelPtr);
            if (modelPtr == nullptr)
            {
                LOGE("modelPtr== nullptr");
                return CZCV_PARAM_ERR;
            }
            s = modelPtr->release(); 
        }
        else
        {
            YoloxTNN * modelPtr = dynamic_cast<YoloxTNN *>(_modelPtr);
            if (modelPtr == nullptr)
            {
                LOGE("modelPtr== nullptr");
                return CZCV_PARAM_ERR;
            }
            s = modelPtr->release(); 
        }
 
        return s;
    }
    
    Status CZCV_Yolox_PersonDetector::on_set_arg()
    {
        // Yolox * modelPtr = dynamic_cast<Yolox *>(_modelPtr);
        // if(modelPtr== nullptr)
        // {
        //     LOGE("modelPtr== nullptr");
        //     return CZCV_PARAM_ERR;
        // }
 

        return CZCV_OK;
    }

    Status CZCV_Yolox_PersonDetector::sub_run(DetInputOutput &inputOutput)
    {
        if (_modelType == EN_MODEL_TYPE_NPU)
        {
            YoloxRKNN * modelPtr = dynamic_cast<YoloxRKNN *>(_modelPtr);
        
            if(modelPtr== nullptr)
            {
                LOGE("modelPtr== nullptr");
                return CZCV_PARAM_ERR;
            }

            Status s;
            s = modelPtr->run(inputOutput);
            if(!s)
            {
                LOGE("stage1 run err!");
                return s;
            }
        }
        else
        {
            YoloxTNN * modelPtr = dynamic_cast<YoloxTNN *>(_modelPtr);
            if(modelPtr== nullptr)
            {
                LOGE("modelPtr== nullptr");
                return CZCV_PARAM_ERR;
            }

            Status s;
            s = modelPtr->run(inputOutput);
            if(!s)
            {
                LOGE("stage1 run err!");
                return s;
            }
        }

        std::vector<BboxF> persons;
        std::vector<BboxF> heads;

        std::vector<BboxF> boxes = inputOutput.bbox();
        for (int i = 0;i < boxes.size(); i++)
        {
            if (boxes[i].class_id() == 0)
            {
                persons.push_back(boxes[i]);
            }
            else if(boxes[i].class_id() == 1) 
            {
                heads.push_back(boxes[i]);
            }
        }

        inputOutput.clean_bbox();
        cv::Mat frame;
        inputOutput.ref_frame_to(frame);
        float areaThres = 0.005f;

        for(int i = 0;i < persons.size(); i++)
        {          
            if (persons[i].width() < frame.cols / 20  && persons[i].height() < frame.rows / 15 )
            {
                continue;
            }

			if (persons[i].area()/(frame.cols*frame.rows)< areaThres)
			{
				continue; 
			}

            inputOutput.push_one_bbox(persons[i]);      
        }

        std::vector<BboxF>::iterator heads_iter;
        for(heads_iter = heads.begin();heads_iter < heads.end(); heads_iter++)
        {
            inputOutput.push_one_bbox(*heads_iter);
        }

        return CZCV_OK;
    }

    REGISTER_DETECTOR(Yolox_Person, DetectorID::Yolox_PERSON);


}//namespace czcv_mobile