// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_MOBILE_INTELLIGENCE_YOLOX_PERSON_DET_H
#define CZCV_MOBILE_INTELLIGENCE_YOLOX_PERSON_DET_H

#include "detector/detector_factory.h"

    
namespace czcv_camera
{
    typedef struct
    {
        int grid0;
        int grid1;
        int stride;
    }GridAndStride;

    typedef struct
    {
        cv::Rect_<float> rect;
        int label;
        float prob;
    }Object;
    DECLARE_DETECTOR(Yolox_Person);

}//namespace czcv_mobile

#endif //CZCV_MOBILE_INTELLIGENCE_YOLOX_PERSON_DET_H
