// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_HAND_RKNN_YOLOV10_H
#define CZCV_HAND_RKNN_YOLOV10_H

#include <vector>
#include <string>
#include <fstream>

#include <opencv2/opencv.hpp>
#include <base/common.h>
#include <base/status.h>
#include <base/abstract_model.h>
#include <detector/base_detector.h>
#include <tracker/base_tracker.h>
#include "rknn_api.h"


namespace czcv_camera
{
	
	typedef struct 
	{
		Rectf rect;
		float score;
		int label;
	} DetectionResult;
	
    class Yolov10RKNN: public AbstarctModel
    {
    public:
		Yolov10RKNN() {LOGE("Yolov10RKNN::Yolov10RKNN\n");};
		~Yolov10RKNN() {LOGE("Yolov10RKNN::~Yolov10RKNN\n");};
		Status release() 
		{
			rknn_destroy_mem(ctx, input_mems[0]);
			rknn_destroy_mem(ctx, output_mems[0]);
			
			rknn_destroy(ctx);

			return CZCV_OK;
		}
		
        Status run(TrackerInputOutput &inputOutput);
		Status run_sub(const cv::Mat & bgr, std::vector<DetectionResult>& results_filtered);
		Status run_sub_rga(float scale, std::vector<DetectionResult>& results_filtered);
		Status run_sub_rga_qat(float scale, std::vector<DetectionResult>& results_filtered);
        Status init(std::vector<std::string> modelConfig,std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType);// override;
		
        void  conf_thres(float v){_confThres = v;}
        float conf_thres() const{return  _confThres;}

        void  nms_thres(float v){_nmsThres   = v;}
        float nms_thres() const {return  _nmsThres;}

		void set_primary(bool v) {_is_primary = v;}
		bool is_primary() const {return _is_primary;}

    private:
		std::string fdLoadFile(std::string path);
        float _confThres = 0.5f;
        float _nmsThres = 0.7f;
        int _inputChannel;
		int _inputWidth;
		int _inputHeight;
		int _boxNum;
		int _outputDim2;
		int _maxDet = 300;
		rknn_input_output_num io_num;
		std::vector<rknn_tensor_attr> input_attrs;
		std::vector<rknn_tensor_attr> output_attrs;
		rknn_context ctx;
		rknn_tensor_mem* input_mems[1];
		rknn_tensor_mem* output_mems[1];
		//czcv_camera::RgaMat rgamat_det_pad;
		std::shared_ptr<rga_interface_t> _rgaInterfacePtr = nullptr;
		bool _is_primary = false;
		bool _rga_flag = true;
		bool _bqat = false;
		int _classNum;
		int _boxDim;
    };
}

#endif //CZCV_HAND_RKNN_YOLOV10_H
