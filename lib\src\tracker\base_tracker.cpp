// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include "tracker/base_tracker.h"
#include <tracker/tracker_factory.h>

namespace czcv_camera
{
    BaseTracker::~BaseTracker()
    {
        if(_modelPtr!= nullptr)
        {            
            delete _modelPtr;
            _modelPtr = nullptr;
        }
    }

    /**
     * @brief init model with config
     * @param modelConfig [in] model or config path
     * @return see Status,CZCV_OK if success
     */
    Status BaseTracker::init(std::vector<std::string> modelConfig)
    {
        _modelConfig = modelConfig;
        _modelName = "CZCV_Default_Tracker";

        return  CZCV_OK;
    }
    /***************************************************/

    /**
     * @brief run detector
     * @param inputOutput [inout] see TrackerInputOutput
     * @param params [in] with hyper params
     * @return see Status,CZCV_OK if success
     */
    Status BaseTracker::run(TrackerInputOutput &inputOutput, czcv_camera::DynamicParams &params,int use_4k)
    {
        // if(!inputOutput._frame.data)
        // {
        //     LOGE("inputOutput._frame.data is nullptr\n");
        //     return CZCV_PARAM_ERR;
        // }
        if(_detector == nullptr)
        {
            LOGE("_detector is nullptr, you must bind a detector\n");
            return CZCV_PARAM_ERR;
        }
        _params = params;
        // on_set_arg();
        double t = (double)cv::getTickCount();
        
        Status s = sub_run(inputOutput,use_4k);

        t = (double)cv::getTickCount() - t;
        t = t / cv::getTickFrequency();

        _profileData.set_fps(1.0/t, 0.5f);

        timeval timestamp;
        gettimeofday(&timestamp, nullptr);
        inputOutput.processed_timestamp(timestamp);
        return s;
    }
    /***************************************************/

    Status BaseTracker::on_set_arg()
    {
        return  CZCV_OK;
    }
    /***************************************************/

    Status BaseTracker::sub_run(TrackerInputOutput &inputOutput,int use_4k)
    {
        return  CZCV_OK;
    }
    /***************************************************/

    TrackerRegister<TempalteTrackerCreator<BaseTracker>>  g_czcv_base_tracker_register(TrackerID::DefaultTracker);


}//namespace czcv_mobile