#include "byte_tracker.h"
    
namespace czcv_camera
{
    Status CZCV_BYTETracker::init(std::vector<std::string> modelConfig)
    {
		frame_id = 0;
        return CZCV_OK;
    }

    Status CZCV_BYTETracker::on_set_arg()
    {
        // track_thresh = _params.get("_person_tracker_track_thresh_",0.3f).AnyCast<float>();
		// low_thresh	= _params.get("_person_tracker_low_thresh_",0.2f).AnyCast<float>();
		// det_thresh = track_thresh + 0.1;
		// match_thresh = _params.get("_person_tracker_match_thresh_",0.8f).AnyCast<float>();
		// iou_thresh = _params.get("_person_tracker_iou_thresh_",0.15f).AnyCast<float>();
		
		// max_time_lost = int(_params.get("_person_tracker_max_time_lost_",90).AnyCast<int>() / 30.0 * 30);
        
		return C<PERSON><PERSON>V_OK;
    }


    vector<STrack*> CZCV_BYTETracker::joint_stracks(vector<STrack*> &tlista, vector<STrack> &tlistb)
	{
		map<int, int> exists;
		vector<STrack*> res;
		for (unsigned int i = 0; i < tlista.size(); i++)
		{
			exists.insert(pair<int, int>(tlista[i]->track_id, 1));
			res.push_back(tlista[i]);
		}
		for (unsigned int i = 0; i < tlistb.size(); i++)
		{
			int tid = tlistb[i].track_id;
			if (!exists[tid] || exists.count(tid) == 0)
			{
				exists[tid] = 1;
				res.push_back(&tlistb[i]);
			}
		}
		return res;
	}

	vector<STrack> CZCV_BYTETracker::joint_stracks(vector<STrack> &tlista, vector<STrack> &tlistb)
	{
		map<int, int> exists;
		vector<STrack> res;
		for (unsigned int i = 0; i < tlista.size(); i++)
		{
			exists.insert(pair<int, int>(tlista[i].track_id, 1));
			res.push_back(tlista[i]);
		}
		for (unsigned int i = 0; i < tlistb.size(); i++)
		{
			int tid = tlistb[i].track_id;
			if (!exists[tid] || exists.count(tid) == 0)
			{
				exists[tid] = 1;
				res.push_back(tlistb[i]);
			}
		}
		return res;
	}

	vector<STrack> CZCV_BYTETracker::sub_stracks(vector<STrack> &tlista, vector<STrack> &tlistb)
	{
		map<int, STrack> stracks;
		for (unsigned int i = 0; i < tlista.size(); i++)
		{
			stracks.insert(pair<int, STrack>(tlista[i].track_id, tlista[i]));
		}
		for (unsigned int i = 0; i < tlistb.size(); i++)
		{
			int tid = tlistb[i].track_id;
			if (stracks.count(tid) != 0)
			{
				stracks.erase(tid);
			}
		}

		vector<STrack> res;
		std::map<int, STrack>::iterator  it;
		for (it = stracks.begin(); it != stracks.end(); ++it)
		{
			res.push_back(it->second);
		}

		return res;
	}

	void CZCV_BYTETracker::remove_duplicate_stracks(vector<STrack> &resa, vector<STrack> &resb, vector<STrack> &stracksa, vector<STrack> &stracksb)
	{
		vector<vector<float> > pdist = iou_distance(stracksa, stracksb);
		vector<pair<int, int> > pairs;
		for (unsigned int i = 0; i < pdist.size(); i++)
		{
			for (unsigned int j = 0; j < pdist[i].size(); j++)
			{
				if (pdist[i][j] < iou_thresh)
				{
					pairs.push_back(pair<int, int>(i, j));
				}
			}
		}

		vector<int> dupa, dupb;
		for (unsigned int i = 0; i < pairs.size(); i++)
		{
			int timep = stracksa[pairs[i].first].frame_id - stracksa[pairs[i].first].start_frame;
			int timeq = stracksb[pairs[i].second].frame_id - stracksb[pairs[i].second].start_frame;
			if (timep > timeq)
				dupb.push_back(pairs[i].second);
			else
				dupa.push_back(pairs[i].first);
		}

		for (unsigned int i = 0; i < stracksa.size(); i++)
		{
			vector<int>::iterator iter = find(dupa.begin(), dupa.end(), i);
			if (iter == dupa.end())
			{
				resa.push_back(stracksa[i]);
			}
		}

		for (unsigned int i = 0; i < stracksb.size(); i++)
		{
			vector<int>::iterator iter = find(dupb.begin(), dupb.end(), i);
			if (iter == dupb.end())
			{
				resb.push_back(stracksb[i]);
			}
		}
	}

	void CZCV_BYTETracker::linear_assignment(vector<vector<float> > &cost_matrix, int cost_matrix_size, int cost_matrix_size_size, float thresh,
		vector<vector<int> > &matches, vector<int> &unmatched_a, vector<int> &unmatched_b)
	{
		if (cost_matrix.size() == 0)
		{
			for (int i = 0; i < cost_matrix_size; i++)
			{
				unmatched_a.push_back(i);
			}
			for (int i = 0; i < cost_matrix_size_size; i++)
			{
				unmatched_b.push_back(i);
			}
			return;
		}

		vector<int> rowsol; vector<int> colsol;
		float c = lapjv(cost_matrix, rowsol, colsol, true, thresh);
		for (unsigned int i = 0; i < rowsol.size(); i++)
		{
			if (rowsol[i] >= 0)
			{
				vector<int> match;
				match.push_back(i);
				match.push_back(rowsol[i]);
				matches.push_back(match);
			}
			else
			{
				unmatched_a.push_back(i);
			}
		}

		for (unsigned int i = 0; i < colsol.size(); i++)
		{
			if (colsol[i] < 0)
			{
				unmatched_b.push_back(i);
			}
		}
	}

	vector<vector<float> > CZCV_BYTETracker::ious(vector<vector<float> > &atlbrs, vector<vector<float> > &btlbrs)
	{
		vector<vector<float> > ious;
		if (atlbrs.size()*btlbrs.size() == 0)
			return ious;

		ious.resize(atlbrs.size());
		for (unsigned int i = 0; i < ious.size(); i++)
		{
			ious[i].resize(btlbrs.size());
		}

		//bbox_ious
		for (unsigned int k = 0; k < btlbrs.size(); k++)
		{
			vector<float> ious_tmp;
			float box_area = (btlbrs[k][2] - btlbrs[k][0] + 1)*(btlbrs[k][3] - btlbrs[k][1] + 1);
			for (unsigned int n = 0; n < atlbrs.size(); n++)
			{
				float iw = min(atlbrs[n][2], btlbrs[k][2]) - max(atlbrs[n][0], btlbrs[k][0]) + 1;
				if (iw > 0)
				{
					float ih = min(atlbrs[n][3], btlbrs[k][3]) - max(atlbrs[n][1], btlbrs[k][1]) + 1;
					if(ih > 0)
					{
						float ua = (atlbrs[n][2] - atlbrs[n][0] + 1)*(atlbrs[n][3] - atlbrs[n][1] + 1) + box_area - iw * ih;
						ious[n][k] = iw * ih / ua;
					}
					else
					{
						ious[n][k] = 0.0;
					}
				}
				else
				{
					ious[n][k] = 0.0;
				}
			}
		}

		return ious;
	}

	vector<vector<float> > CZCV_BYTETracker::iou_distance(vector<STrack*> &atracks, vector<STrack> &btracks, int &dist_size, int &dist_size_size)
	{
		vector<vector<float> > cost_matrix;
		if (atracks.size() * btracks.size() == 0)
		{
			dist_size = atracks.size();
			dist_size_size = btracks.size();
			return cost_matrix;
		}
		vector<vector<float> > atlbrs, btlbrs;
		for (unsigned int i = 0; i < atracks.size(); i++)
		{
			atlbrs.push_back(atracks[i]->tlbr);
		}
		for (unsigned int i = 0; i < btracks.size(); i++)
		{
			btlbrs.push_back(btracks[i].tlbr);
		}

		dist_size = atracks.size();
		dist_size_size = btracks.size();

		vector<vector<float> > _ious = ious(atlbrs, btlbrs);
		
		for (unsigned int i = 0; i < _ious.size();i++)
		{
			vector<float> _iou;
			for (unsigned int j = 0; j < _ious[i].size(); j++)
			{
				_iou.push_back(1 - _ious[i][j]);
			}
			cost_matrix.push_back(_iou);
		}

		return cost_matrix;
	}

	vector<vector<float> > CZCV_BYTETracker::iou_distance(vector<STrack> &atracks, vector<STrack> &btracks)
	{
		vector<vector<float> > atlbrs, btlbrs;
		for (unsigned int i = 0; i < atracks.size(); i++)
		{
			atlbrs.push_back(atracks[i].tlbr);
		}
		for (unsigned int i = 0; i < btracks.size(); i++)
		{
			btlbrs.push_back(btracks[i].tlbr);
		}

		vector<vector<float> > _ious = ious(atlbrs, btlbrs);
		vector<vector<float> > cost_matrix;
		for (unsigned int i = 0; i < _ious.size(); i++)
		{
			vector<float> _iou;
			for (unsigned int j = 0; j < _ious[i].size(); j++)
			{
				_iou.push_back(1 - _ious[i][j]);
			}
			cost_matrix.push_back(_iou);
		}

		return cost_matrix;
	}

	double CZCV_BYTETracker::lapjv(const vector<vector<float> > &cost, vector<int> &rowsol, vector<int> &colsol,
		bool extend_cost, float cost_limit, bool return_cost)
	{
		vector<vector<float> > cost_c;
		cost_c.assign(cost.begin(), cost.end());

		vector<vector<float> > cost_c_extended;

		int n_rows = cost.size();
		int n_cols = cost[0].size();
		rowsol.resize(n_rows);
		colsol.resize(n_cols);

		int n = 0;
		if (n_rows == n_cols)
		{
			n = n_rows;
		}
		else
		{
			if (!extend_cost)
			{
				cout << "set extend_cost=True" << endl;
				exit(0);
			}
		}
			
		if (extend_cost || cost_limit < LONG_MAX)
		{
			n = n_rows + n_cols;
			cost_c_extended.resize(n);
			for (unsigned int i = 0; i < cost_c_extended.size(); i++)
				cost_c_extended[i].resize(n);

			if (cost_limit < LONG_MAX)
			{
				for (unsigned int i = 0; i < cost_c_extended.size(); i++)
				{
					for (unsigned int j = 0; j < cost_c_extended[i].size(); j++)
					{
						cost_c_extended[i][j] = cost_limit / 2.0;
					}
				}
			}
			else
			{
				float cost_max = -1;
				for (unsigned int i = 0; i < cost_c.size(); i++)
				{
					for (unsigned int j = 0; j < cost_c[i].size(); j++)
					{
						if (cost_c[i][j] > cost_max)
							cost_max = cost_c[i][j];
					}
				}
				for (unsigned int i = 0; i < cost_c_extended.size(); i++)
				{
					for (unsigned int j = 0; j < cost_c_extended[i].size(); j++)
					{
						cost_c_extended[i][j] = cost_max + 1;
					}
				}
			}

			for (unsigned int i = n_rows; i < cost_c_extended.size(); i++)
			{
				for (unsigned int j = n_cols; j < cost_c_extended[i].size(); j++)
				{
					cost_c_extended[i][j] = 0;
				}
			}
			for (int i = 0; i < n_rows; i++)
			{
				for (int j = 0; j < n_cols; j++)
				{
					cost_c_extended[i][j] = cost_c[i][j];
				}
			}

			cost_c.clear();
			cost_c.assign(cost_c_extended.begin(), cost_c_extended.end());
		}

		double **cost_ptr;
		cost_ptr = new double *[sizeof(double *) * n];
		for (int i = 0; i < n; i++)
			cost_ptr[i] = new double[sizeof(double) * n];

		for (int i = 0; i < n; i++)
		{
			for (int j = 0; j < n; j++)
			{
				cost_ptr[i][j] = cost_c[i][j];
			}
		}

		int* x_c = new int[sizeof(int) * n];
		int *y_c = new int[sizeof(int) * n];

		int ret = czcv_camera::lapjv_internal(n, cost_ptr, x_c, y_c);
		if (ret != 0)
		{
			cout << "Calculate Wrong!" << endl;
			exit(0);
		}

		double opt = 0.0;

		if (n != n_rows)
		{
			for (int i = 0; i < n; i++)
			{
				if (x_c[i] >= n_cols)
					x_c[i] = -1;
				if (y_c[i] >= n_rows)
					y_c[i] = -1;
			}
			for (int i = 0; i < n_rows; i++)
			{
				rowsol[i] = x_c[i];
			}
			for (int i = 0; i < n_cols; i++)
			{
				colsol[i] = y_c[i];
			}

			if (return_cost)
			{
				for (unsigned int i = 0; i < rowsol.size(); i++)
				{
					if (rowsol[i] != -1)
					{
						//cout << i << "\t" << rowsol[i] << "\t" << cost_ptr[i][rowsol[i]] << endl;
						opt += cost_ptr[i][rowsol[i]];
					}
				}
			}
		}
		else if (return_cost)
		{
			for (unsigned int i = 0; i < rowsol.size(); i++)
			{
				opt += cost_ptr[i][rowsol[i]];
			}
		}

		for (int i = 0; i < n; i++)
		{
			delete[]cost_ptr[i];
		}
		delete[]cost_ptr;
		delete[]x_c;
		delete[]y_c;

		return opt;
	}

    Status CZCV_BYTETracker::sub_run(TrackerInputOutput &inputOutput,int use_4k)
    {
        //initial state
        ////////////////// Step 1: Get detections //////////////////
		this->frame_id++;
		vector<STrack> activated_stracks;
		vector<STrack> refind_stracks;
		vector<STrack> removed_stracks;
		vector<STrack> lost_stracks;
		vector<STrack> detections;
		vector<STrack> detections_low;

		vector<STrack> detections_cp;
		vector<STrack> tracked_stracks_swap;
		vector<STrack> resa, resb;
		vector<STrack> output_stracks;

		vector<STrack*> unconfirmed;
		vector<STrack*> tracked_stracks;
		vector<STrack*> strack_pool;
		vector<STrack*> r_tracked_stracks;

		std::vector<BboxF> in_boxes = inputOutput.in_bbox();
		if (in_boxes.size() > 0)
		{
			for (unsigned int i = 0; i < in_boxes.size(); i++)
			{
				vector<float> tlbr_;
				tlbr_.resize(4);
				tlbr_[0] = in_boxes[i].cv_rect().x;
				tlbr_[1] = in_boxes[i].cv_rect().y;
				tlbr_[2] = in_boxes[i].cv_rect().x + in_boxes[i].cv_rect().width;
				tlbr_[3] = in_boxes[i].cv_rect().y + in_boxes[i].cv_rect().height;
				float score = in_boxes[i].score();

				STrack strack(STrack::tlbr_to_tlwh(tlbr_), score, in_boxes[i].class_id(), i);

				if (score >= track_thresh)
				{
					detections.push_back(strack);
				}
				else if(score >= low_thresh)
				{
					detections_low.push_back(strack);
				}
				
			}
		}

		// Add newly detected tracklets to tracked_stracks
		for (unsigned int i = 0; i < this->tracked_stracks.size(); i++)
		{
			if (!this->tracked_stracks[i].is_activated)
				unconfirmed.push_back(&this->tracked_stracks[i]);
			else
				tracked_stracks.push_back(&this->tracked_stracks[i]);
		}

		////////////////// Step 2: First association, with IoU //////////////////
		strack_pool = joint_stracks(tracked_stracks, this->lost_stracks);
		STrack::multi_predict(strack_pool, this->kalman_filter);

		vector<vector<float> > dists;
		int dist_size = 0, dist_size_size = 0;
		dists = iou_distance(strack_pool, detections, dist_size, dist_size_size);

		vector<vector<int> > matches;
		vector<int> u_track, u_detection;
		linear_assignment(dists, dist_size, dist_size_size, match_thresh, matches, u_track, u_detection);
		for (unsigned int i = 0; i < matches.size(); i++)
		{
			STrack *track = strack_pool[matches[i][0]];
			STrack *det = &detections[matches[i][1]];
			if (track->state == TrackState::Tracked)
			{
				track->update(*det, this->frame_id);
				activated_stracks.push_back(*track);
			}
			else
			{
				track->re_activate(*det, this->frame_id, false);
				refind_stracks.push_back(*track);
			}
		}

		////////////////// Step 3: Second association, using low score dets //////////////////
		for (unsigned int i = 0; i < u_detection.size(); i++)
		{
			detections_cp.push_back(detections[u_detection[i]]);
		}
		detections.clear();
		detections.assign(detections_low.begin(), detections_low.end());
		
		for (unsigned int i = 0; i < u_track.size(); i++)
		{
			if (strack_pool[u_track[i]]->state == TrackState::Tracked)
			{
				r_tracked_stracks.push_back(strack_pool[u_track[i]]);
			}
		}

		dists.clear();
		dists = iou_distance(r_tracked_stracks, detections, dist_size, dist_size_size);

		matches.clear();
		u_track.clear();
		u_detection.clear();
		linear_assignment(dists, dist_size, dist_size_size, 0.5f, matches, u_track, u_detection);

		for (unsigned int i = 0; i < matches.size(); i++)
		{
			STrack *track = r_tracked_stracks[matches[i][0]];
			STrack *det = &detections[matches[i][1]];
			if (track->state == TrackState::Tracked)
			{
				track->update(*det, this->frame_id);
				activated_stracks.push_back(*track);
			}
			else
			{
				track->re_activate(*det, this->frame_id, false);
				refind_stracks.push_back(*track);
			}
		}

		for (unsigned int i = 0; i < u_track.size(); i++)
		{
			STrack *track = r_tracked_stracks[u_track[i]];
			if (track->state != TrackState::Lost)
			{
				track->mark_lost();
				lost_stracks.push_back(*track);
			}
		}

		// Deal with unconfirmed tracks, usually tracks with only one beginning frame
		detections.clear();
		detections.assign(detections_cp.begin(), detections_cp.end());

		dists.clear();
		dists = iou_distance(unconfirmed, detections, dist_size, dist_size_size);

		matches.clear();
		vector<int> u_unconfirmed;
		u_detection.clear();
		linear_assignment(dists, dist_size, dist_size_size, 0.7, matches, u_unconfirmed, u_detection);

		for (unsigned int i = 0; i < matches.size(); i++)
		{
			unconfirmed[matches[i][0]]->update(detections[matches[i][1]], this->frame_id);
			activated_stracks.push_back(*unconfirmed[matches[i][0]]);
		}

		for (unsigned int i = 0; i < u_unconfirmed.size(); i++)
		{
			STrack *track = unconfirmed[u_unconfirmed[i]];
			track->mark_removed();
			removed_stracks.push_back(*track);
		}

		////////////////// Step 4: Init new stracks //////////////////
		for (unsigned int i = 0; i < u_detection.size(); i++)
		{
			STrack *track = &detections[u_detection[i]];
			if (track->score < this->det_thresh)
				continue;
			
			track->activate(this->kalman_filter, this->frame_id);
			activated_stracks.push_back(*track);
		}

		////////////////// Step 5: Update state //////////////////
		for (unsigned int i = 0; i < this->lost_stracks.size(); i++)
		{
			if (this->frame_id - this->lost_stracks[i].end_frame() > this->max_time_lost)
			{
				this->lost_stracks[i].mark_removed();
				removed_stracks.push_back(this->lost_stracks[i]);
			}
		}
		
		for (unsigned int i = 0; i < this->tracked_stracks.size(); i++)
		{
			if (this->tracked_stracks[i].state == TrackState::Tracked)
			{
				tracked_stracks_swap.push_back(this->tracked_stracks[i]);
			}
		}
		this->tracked_stracks.clear();
		this->tracked_stracks.assign(tracked_stracks_swap.begin(), tracked_stracks_swap.end());

		this->tracked_stracks = joint_stracks(this->tracked_stracks, activated_stracks);
		this->tracked_stracks = joint_stracks(this->tracked_stracks, refind_stracks);

		this->lost_stracks = sub_stracks(this->lost_stracks, this->tracked_stracks);
		for (unsigned int i = 0; i < lost_stracks.size(); i++)
		{
			this->lost_stracks.push_back(lost_stracks[i]);
		}

		this->lost_stracks = sub_stracks(this->lost_stracks, this->removed_stracks);
		for (unsigned int i = 0; i < removed_stracks.size(); i++)
		{
			this->removed_stracks.push_back(removed_stracks[i]);
		}
		
		remove_duplicate_stracks(resa, resb, this->tracked_stracks, this->lost_stracks);

		// this->tracked_stracks.clear();
		// this->tracked_stracks.assign(resa.begin(), resa.end());
		this->lost_stracks.clear();
		this->lost_stracks.assign(resb.begin(), resb.end());
        inputOutput.clean_tracked_bbox();
		inputOutput.clean_in_bbox();

		int frameW;
		int frameH;
		if (!use_4k)
		{
			frameW = inputOutput.frame().cols;
			frameH = inputOutput.frame().rows;
		}
		else
		{
			frameW = 3840;
			frameH = 2160;
		}
		for (unsigned int i = 0; i < resa.size(); i++)
		{
			if (resa[i].is_activated)
			{
				float xmin = (std::min)((std::max)(0.0f, resa[i].tlwh[0]), (float)(frameW - 1));
                float xmax = (std::min)((std::max)(0.0f, resa[i].tlwh[0]+resa[i].tlwh[2]), (float)(frameW - 1));
				float ymin = (std::min)((std::max)(0.0f, resa[i].tlwh[1]), (float)(frameH - 1));
				float ymax = (std::min)((std::max)(0.0f, resa[i].tlwh[1]+resa[i].tlwh[3]), (float)(frameH - 1));
				BboxF bboxF(xmin, ymin, xmax, ymax, resa[i].score,resa[i].label,resa[i].track_id );
				inputOutput.push_tracked_bbox(bboxF);

				xmin = (std::max)(0.0f, (float)in_boxes[resa[i]._detid].xmin());
				xmin = (std::min)(xmin, (float)(frameW - 1));
				xmax = (std::max)(0.0f, (float)in_boxes[resa[i]._detid].xmax());
				xmax = (std::min)(xmax, (float)(frameW - 1));
				ymin = (std::max)(0.0f, (float)in_boxes[resa[i]._detid].ymin());
				ymin = (std::min)(ymin, (float)(frameH - 1));
				ymax = (std::max)(0.0f, (float)in_boxes[resa[i]._detid].ymax());
				ymax = (std::min)(ymax, (float)(frameH - 1));
				BboxF detbboxF(xmin, ymin, xmax, ymax, resa[i].score,resa[i].label,resa[i].track_id );

				inputOutput.push_in_bbox(detbboxF);		
			}
		}
	
        return CZCV_OK;
    }

    TrackerRegister<TempalteTrackerCreator<CZCV_BYTETracker>>  g_czcv_byte_tracker_register(TrackerID::BYTE);


    STrack::STrack(vector<float> tlwh_, float score, int label, int detid)
	{
		_tlwh.resize(4);
		_tlwh.assign(tlwh_.begin(), tlwh_.end());

		is_activated = false;
		track_id = 0;
		state = TrackState::New;
		
		tlwh.resize(4);
		tlbr.resize(4);

		static_tlwh();
		static_tlbr();
		frame_id = 0;
		tracklet_len = 0;
		this->score = score;
		start_frame = 0;
		this->label = label;
		_detid = detid;
	}

	STrack::~STrack()
	{
	}

	void STrack::activate(czcv_camera::KalmanFilter &kalman_filter, int frame_id)
	{
		this->kalman_filter = kalman_filter;
		this->track_id = this->next_id();
		
		vector<float> _tlwh_tmp(4);
		_tlwh_tmp[0] = this->_tlwh[0];
		_tlwh_tmp[1] = this->_tlwh[1];
		_tlwh_tmp[2] = this->_tlwh[2];
		_tlwh_tmp[3] = this->_tlwh[3];
		vector<float> xyah = tlwh_to_xyah(_tlwh_tmp);
		czcv_camera::DETECTBOX xyah_box;
		xyah_box[0] = xyah[0];
		xyah_box[1] = xyah[1];
		xyah_box[2] = xyah[2];
		xyah_box[3] = xyah[3];
		auto mc = this->kalman_filter.initiate(xyah_box);
		this->mean = mc.first;
		this->covariance = mc.second;

		static_tlwh();
		static_tlbr();

		this->tracklet_len = 0;
		this->state = TrackState::Tracked;
		// if (frame_id == 1)
		// {
		// 	this->is_activated = true;
		// }
		this->is_activated = true;
		this->frame_id = frame_id;
		this->start_frame = frame_id;
	}

	void STrack::re_activate(STrack &new_track, int frame_id, bool new_id)
	{
		vector<float> xyah = tlwh_to_xyah(new_track.tlwh);
		czcv_camera::DETECTBOX xyah_box;
		xyah_box[0] = xyah[0];
		xyah_box[1] = xyah[1];
		xyah_box[2] = xyah[2];
		xyah_box[3] = xyah[3];
		auto mc = this->kalman_filter.update(this->mean, this->covariance, xyah_box);
		this->mean = mc.first;
		this->covariance = mc.second;

		static_tlwh();
		static_tlbr();

		this->tracklet_len = 0;
		this->state = TrackState::Tracked;
		this->is_activated = true;
		this->frame_id = frame_id;
		this->score = new_track.score;
		if (new_id)
			this->track_id = next_id();
		this->_detid = new_track._detid;
	}

	void STrack::update(STrack &new_track, int frame_id)
	{
		this->frame_id = frame_id;
		this->tracklet_len++;

		vector<float> xyah = tlwh_to_xyah(new_track.tlwh);
		czcv_camera::DETECTBOX xyah_box;
		xyah_box[0] = xyah[0];
		xyah_box[1] = xyah[1];
		xyah_box[2] = xyah[2];
		xyah_box[3] = xyah[3];

		auto mc = this->kalman_filter.update(this->mean, this->covariance, xyah_box);
		this->mean = mc.first;
		this->covariance = mc.second;

		static_tlwh();
		static_tlbr();

		this->state = TrackState::Tracked;
		this->is_activated = true;

		this->score = new_track.score;
		this->_detid = new_track._detid;
	}

	void STrack::static_tlwh()
	{
		if (this->state == TrackState::New)
		{
			tlwh[0] = _tlwh[0];
			tlwh[1] = _tlwh[1];
			tlwh[2] = _tlwh[2];
			tlwh[3] = _tlwh[3];
			return;
		}

		tlwh[0] = mean[0];
		tlwh[1] = mean[1];
		tlwh[2] = mean[2];
		tlwh[3] = mean[3];

		tlwh[2] *= tlwh[3];
		tlwh[0] -= tlwh[2] / 2;
		tlwh[1] -= tlwh[3] / 2;
	}

	void STrack::static_tlbr()
	{
		tlbr.clear();
		tlbr.assign(tlwh.begin(), tlwh.end());
		tlbr[2] += tlbr[0];
		tlbr[3] += tlbr[1];
	}

	vector<float> STrack::tlwh_to_xyah(vector<float> tlwh_tmp)
	{
		vector<float> tlwh_output = tlwh_tmp;
		tlwh_output[0] += tlwh_output[2] / 2;
		tlwh_output[1] += tlwh_output[3] / 2;
		tlwh_output[2] /= tlwh_output[3];
		return tlwh_output;
	}

	vector<float> STrack::to_xyah()
	{
		return tlwh_to_xyah(tlwh);
	}

	vector<float> STrack::tlbr_to_tlwh(vector<float> &tlbr)
	{
		tlbr[2] -= tlbr[0];
		tlbr[3] -= tlbr[1];
		return tlbr;
	}

	void STrack::mark_lost()
	{
		state = TrackState::Lost;
	}

	void STrack::mark_removed()
	{
		state = TrackState::Removed;
	}

	int STrack::next_id()
	{
		static int _count = 0;
		_count++;
		if(_count > (INT32_MAX - 1))
		{
			_count = 0;
		}
		return _count;
	}

	int STrack::end_frame()
	{
		return this->frame_id;
	}

	void STrack::multi_predict(vector<STrack*> &stracks, czcv_camera::KalmanFilter &kalman_filter)
	{
		for (unsigned int i = 0; i < stracks.size(); i++)
		{
			if (stracks[i]->state != TrackState::Tracked)
			{
				stracks[i]->mean[7] = 0;
			}
			kalman_filter.predict(stracks[i]->mean, stracks[i]->covariance);
		}
	}
}