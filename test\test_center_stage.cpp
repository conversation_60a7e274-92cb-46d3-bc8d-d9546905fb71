// Copyright (C) 2021 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include "center_stage/czcv_center_stage.h"
#include "config/config_setter.h"
//modify to your own
//#define  MODEL_DIR std::string("/Users/<USER>/Documents/01_my_work/czcv_mobile_intelligence/models/center_stage/")
#define  MODEL_DIR std::string("D:/Program/Project/project/czcv_mobile_meeting_alg/models/")


static void nv21resize(cv::Mat& nv21, cv::Mat& dstnv21, int dstwidth, int dstheight, cv::Rect& rect, cv::Rect& dstrect)
{
    int rows = nv21.rows * 2 / 3;
    int cols = nv21.cols;
    cv::Mat y(rows, cols, CV_8UC1, nv21.data);
    cv::Mat vu(rows / 2, cols / 2, CV_8UC2, ((unsigned char*)nv21.data + rows * cols));

    cv::Mat dsty(dstheight, dstwidth, CV_8UC1, dstnv21.data);
    cv::Mat dstvu(dstheight / 2, dstwidth / 2, CV_8UC2, (unsigned char*)dstnv21.data + dstwidth * dstheight);
 
    cv::resize(y(rect), dsty(dstrect), cv::Size(dstrect.width, dstrect.height));
    cv::Rect vurect(rect.x / 2, rect.y / 2, rect.width / 2, rect.height / 2);   
    cv::Rect dstvurect(dstrect.x / 2, dstrect.y / 2, dstrect.width / 2, dstrect.height / 2);
    cv::resize(vu(vurect), dstvu(dstvurect), cv::Size(dstvurect.width, dstvurect.height));   
}


void bgr2nv21(cv::Mat bgr, cv::Mat& nv21)
{
    cv::Mat yuv;
    cv::cvtColor(bgr, yuv, cv::COLOR_BGR2YUV_YV12);
   
    nv21 = cv::Mat::zeros(yuv.rows, yuv.cols, CV_8UC1);
    memcpy(nv21.data, yuv.data, bgr.rows * bgr.cols);
    unsigned char* pnv21v = (unsigned char*)nv21.data + bgr.rows * bgr.cols;
    unsigned char* pnv21u = (unsigned char*)nv21.data + bgr.rows * bgr.cols + 1;
    unsigned char* pyuv_v = (unsigned char*)yuv.data + bgr.rows * bgr.cols;
    unsigned char* pyuv_u = (unsigned char*)yuv.data + bgr.rows * bgr.cols + bgr.rows * bgr.cols / 4;
    for (size_t i = 0; i < bgr.rows * bgr.cols / 4; i++)
    {
        *pnv21v = *pyuv_v;
        *pnv21u = *pyuv_u;

        pyuv_v++;
        pyuv_u++;
        pnv21v += 2;
        pnv21u += 2;
    }
}

void test()
{
    cv::Mat img = cv::imread("D:/Dataset/a0002.jpg");
    cv::resize(img, img, cv::Size(1920, 1080));

    cv::Mat nv21;
    bgr2nv21(img, nv21);

    int dstwidth = 640;
    int dstheight = 480;


    cv::Mat dstnv21 = cv::Mat::zeros(dstheight * 3 / 2, dstwidth, CV_8UC1);

    double t1 = cv::getTickCount();
    cv::Rect rect(0, 0, 1280, 960);
    cv::Rect dstrect(0, 0, 320, 240);
    nv21resize(nv21, dstnv21, dstwidth, dstheight, rect, dstrect);
    double t2 = cv::getTickCount();

    cv::Mat dstbgr;
    cv::cvtColor(dstnv21, dstbgr, cv::COLOR_YUV2BGR_NV21);
    cv::imshow("img", dstbgr);
    cv::waitKey();
}

int  main()
{

    int  frameW = 1920;
    int  frameH = 1080;
    // base viewer 是简单的拿第一个window做crop,resize回原大小
    std::shared_ptr<czcv_camera::Base_PersonViewer>  personViewer =
            std::make_shared<czcv_camera::Base_PersonViewer>();

    // base是无畸变的，直接做了个copy
    std::shared_ptr<czcv_camera::BaseCamDewarper>  dewarper =
            std::make_shared<czcv_camera::BaseCamDewarper>();

    //dewarper->init_models(MODEL_DIR+"/dewarp_model.dat");

    // 采用demo 版的data callback
    std::shared_ptr<czcv_camera::Blocked_BGR_PersonViewer_DataCallback> dataCallback=
            std::make_shared<czcv_camera::Blocked_BGR_PersonViewer_DataCallback>(frameW, frameH);

    std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>  baseDataCallback = dataCallback;

    personViewer->bind_callback(baseDataCallback);
    personViewer->bind_dewarper(dewarper);


    czcv_camera::PersonCenterStager stager(frameW,frameH);
    stager.bind_viewer(personViewer);
   
    //stager.set_detector(czcv_mobile::DetectorID::CenterCrop_Demo_Det);
    stager.set_detector(czcv_camera::DetectorID::Yolox_PERSON);
    stager.set_tracker(czcv_camera::TrackerID::BYTE);

    czcv_camera::DynamicParams params;

    params.set("_person_detect_thresh_", 0.2f);
    params.set("_person_tracker_track_thresh_", 0.3f);
    params.set("_person_tracker_match_thresh_", 0.8f);
    params.set("_person_tracker_iou_thresh_", 0.15f);
    params.set("_person_tracker_max_time_lost_", 15);
    stager.on_set_arg(params);
    

    std::vector<std::string> modelConfig;
    modelConfig.push_back(MODEL_DIR+"/yolox-mobilenet3-person-sim.opt.tnnproto");
    modelConfig.push_back(MODEL_DIR+"/yolox-mobilenet3-person-sim.opt.tnnmodel");
    stager.init_models_async(modelConfig,1);

    std::vector<std::string> modelConfig_assert;
    modelConfig_assert.push_back(MODEL_DIR + "/MobileNetV3-large.opt.tnnproto");
    modelConfig_assert.push_back(MODEL_DIR + "/MobileNetV3-large.opt.tnnmodel");
    stager.init_model_assert(modelConfig_assert,1);
    
    stager.start();

    cv::VideoCapture cap(0);
    cap.set(cv::CAP_PROP_FRAME_WIDTH, frameW);
    cap.set(cv::CAP_PROP_FRAME_HEIGHT, frameH);
 /*   cv::VideoCapture cap;
    cap.open("D:/Dataset/2.avi");*/

    if(!cap.isOpened())
    {
        std::cout<<"open cam failed!"<<std::endl;
        return -1;
    }
   

    cv::namedWindow("src", 0);
    cv::namedWindow("dst", 0);
    while (true)
    {
        cv::Mat frame;
        cap >> frame;
        if(frame.data)
        {
            cv::resize(frame, frame, cv::Size(frameW, frameH));
            cv::Mat rgba;
            //cv::cvtColor(frame, rgba, cv::COLOR_BGR2RGBA);
            bgr2nv21(frame, rgba);
            czcv_camera::ImageBlob imageBlob;
            imageBlob.frameCopiedBGR = rgba.clone();

            stager.run(imageBlob,0,0);

            cv::Mat pulledMat;
            pulledMat = imageBlob.frameCopiedBGR.clone();
            cv::cvtColor(pulledMat, pulledMat, cv::COLOR_YUV2BGR_NV21);

            cv::imshow("src",frame);
            cv::imshow("dst",pulledMat);
            char c = cv::waitKey(15);

            if(c == ' ')
            {
                break;
            }
        }

    }


    return 0;
}