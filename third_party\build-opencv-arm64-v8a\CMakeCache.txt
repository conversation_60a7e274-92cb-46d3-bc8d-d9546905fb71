# This is the CMakeCache file.
# For build in directory: /mnt/d/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//No help, variable specified on the command line.
ANDROID_ABI:UNINITIALIZED=arm64-v8a

//No help, variable specified on the command line.
ANDROID_NATIVE_API_LEVEL:UNINITIALIZED=android-21

//No help, variable specified on the command line.
ANDROID_NDK:UNINITIALIZED=D:\Package\android-ndk-r23c/

//No help, variable specified on the command line.
ANDROID_STL:UNINITIALIZED=c++_shared

//No help, variable specified on the command line.
APP_STL:UNINITIALIZED=c++_shared

//No help, variable specified on the command line.
BUILD_ANDROID_EXAMPLES:UNINITIALIZED=OFF

//No help, variable specified on the command line.
BUILD_ANDROID_PROJECTS:UNINITIALIZED=OFF

//No help, variable specified on the command line.
BUILD_EXAMPLES:UNINITIALIZED=OFF

//No help, variable specified on the command line.
BUILD_JAVA:UNINITIALIZED=OFF

//No help, variable specified on the command line.
BUILD_JPEG:UNINITIALIZED=ON

//No help, variable specified on the command line.
BUILD_LIST:UNINITIALIZED=core,imgproc,imgcodecs,calib3d

//No help, variable specified on the command line.
BUILD_OPENJPEG:UNINITIALIZED=ON

//No help, variable specified on the command line.
BUILD_PERF_TESTS:UNINITIALIZED=OFF

//No help, variable specified on the command line.
BUILD_SHARED_LIBS:UNINITIALIZED=OFF

//No help, variable specified on the command line.
BUILD_TESTS:UNINITIALIZED=OFF

//No help, variable specified on the command line.
BUILD_TIFF:UNINITIALIZED=OFF

//Use symlinks instead of files copying during build (and !!INSTALL!!)
BUILD_USE_SYMLINKS:BOOL=OFF

//No help, variable specified on the command line.
BUILD_ZLIB:UNINITIALIZED=ON

//No help, variable specified on the command line.
BUILD_opencv_apps:UNINITIALIZED=OFF

//No help, variable specified on the command line.
BUILD_opencv_world:UNINITIALIZED=OFF

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:UNINITIALIZED=Release

//Configs
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release

//No help, variable specified on the command line.
CMAKE_INSTALL_PREFIX:UNINITIALIZED=../prebuilt/android_v8a/opencv4.5.1/

//No help, variable specified on the command line.
CMAKE_TOOLCHAIN_FILE:UNINITIALIZED=D:\Package\android-ndk-r23c\build\cmake\android.toolchain.cmake

//No help, variable specified on the command line.
ENABLE_NEON:UNINITIALIZED=OFF

//Generate position independent code (necessary for shared libraries)
ENABLE_PIC:BOOL=ON

//No help, variable specified on the command line.
HAVE_VIDEOIO:UNINITIALIZED=OFF

//Dump called OpenCV hooks
OPENCV_DUMP_HOOKS_FLOW:BOOL=OFF

//No help, variable specified on the command line.
WITH_ITT:UNINITIALIZED=OFF

//No help, variable specified on the command line.
WITH_JPEG:UNINITIALIZED=ON

//No help, variable specified on the command line.
WITH_OPENEXR:UNINITIALIZED=OFF

//No help, variable specified on the command line.
WITH_OPENJPEG:UNINITIALIZED=ON

//No help, variable specified on the command line.
WITH_PROTOBUF:UNINITIALIZED=OFF

//No help, variable specified on the command line.
WITH_QUIRC:UNINITIALIZED=OFF

//No help, variable specified on the command line.
WITH_TBB:UNINITIALIZED=OFF

//No help, variable specified on the command line.
WITH_TIFF:UNINITIALIZED=OFF


########################
# INTERNAL cache entries
########################

//STRINGS property for variable: CMAKE_BUILD_TYPE
CMAKE_BUILD_TYPE-STRINGS:INTERNAL=Debug;Release
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/mnt/d/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=16
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/mnt/d/Program/Project/project/czcv_camera_new/third_party/source/opencv-4.5.1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.16
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
OPENCV_BUILD_INFO_STR:INTERNAL=
OPENCV_DEPHELPER:INTERNAL=/mnt/d/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/dephelper

