2013-02-01  Google Inc. <<EMAIL>>

	* google-glog: version 0.3.3
	* Add --disable-rtti option for configure.
	* Visual Studio build and test fix.
	* QNX build fix (thanks vanuan).
	* Reduce warnings.
	* Fixed LOG_SYSRESULT (thanks ukai).
	* FreeBSD build fix (thanks <PERSON><PERSON><PERSON><PERSON>).
	* Clang build fix.
	* Now users can re-initialize glog after ShutdownGoogleLogging.
	* Color output support by GLOG_colorlogtostderr (thanks alexs).
	* Now glog's ABI around flags are compatible with gflags.
	* Document mentions how to modify flags from user programs.

2012-01-12  Google Inc. <<EMAIL>>

	* google-glog: version 0.3.2
	* Clang support.
	* Demangler and stacktrace improvement for newer GCCs.
	* Now fork(2) doesn't mess up log files.
	* Make valgrind happier.
	* Reduce warnings for more -W options.
	* Provide a workaround for ERROR defined by windows.h.

2010-06-15  Google Inc. <<EMAIL>>

	* google-glog: version 0.3.1
	* GLOG_* environment variables now work even when gflags is installed.
	* Snow leopard support.
	* Now we can build and test from out side tree.
	* Add DCHECK_NOTNULL.
	* Add ShutdownGoogleLogging to close syslog (thanks DGunchev)
	* Fix --enable-frame-pointers option (thanks kazuki.ohta)
	* Fix libunwind detection (thanks giantchen)

2009-07-30  Google Inc. <<EMAIL>>

	* google-glog: version 0.3.0
	* Fix a deadlock happened when user uses glog with recent gflags.
	* Suppress several unnecessary warnings (thanks keir).
	* NetBSD and OpenBSD support.
	* Use Win32API GetComputeNameA properly (thanks magila).
	* Fix user name detection for Windows (thanks ademin).
	* Fix several minor bugs.

2009-04-10  Google Inc. <<EMAIL>>
	* google-glog: version 0.2.1
	* Fix timestamps of VC++ version.
	* Add pkg-config support (thanks Tomasz)
	* Fix build problem when building with gtest (thanks Michael)
	* Add --with-gflags option for configure (thanks Michael)
	* Fixes for GCC 4.4 (thanks John)

2009-01-23  Google Inc. <<EMAIL>>
	* google-glog: version 0.2
	* Add initial Windows VC++ support.
	* Google testing/mocking frameworks integration.
	* Link pthread library automatically.
	* Flush logs in signal handlers.
	* Add macros LOG_TO_STRING, LOG_AT_LEVEL, DVLOG, and LOG_TO_SINK_ONLY.
	* Log microseconds.
	* Add --log_backtrace_at option.
	* Fix some minor bugs.

2008-11-18  Google Inc. <<EMAIL>>
	* google-glog: version 0.1.2
	* Add InstallFailureSignalHandler(). (satorux)
	* Re-organize the way to produce stacktraces.
	* Don't define unnecessary macro DISALLOW_EVIL_CONSTRUCTORS.

2008-10-15  Google Inc. <<EMAIL>>
	* google-glog: version 0.1.1
	* Support symbolize for MacOSX 10.5.
	* BUG FIX: --vmodule didn't work with gflags.
	* BUG FIX: symbolize_unittest failed with GCC 4.3.
	* Several fixes on the document.

2008-10-07  Google Inc. <<EMAIL>>

	* google-glog: initial release:
	The glog package contains a library that implements application-level
	logging.  This library provides logging APIs based on C++-style
	streams and various helper macros.
