# Makefile.in generated by automake 1.14.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2013 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@





VPATH = @srcdir@
am__is_gnu_make = test -n '$(MAKEFILE_LIST)' && test -n '$(MAKELEVEL)'
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@

# These are good warnings to turn on by default
@GCC_TRUE@am__append_1 = -Wall -Wwrite-strings -Woverloaded-virtual -Wno-sign-compare

# These are x86-specific, having to do with frame-pointers
@ENABLE_FRAME_POINTERS_TRUE@@X86_64_TRUE@am__append_2 = -fno-omit-frame-pointer
@ENABLE_FRAME_POINTERS_FALSE@@X86_64_TRUE@am__append_3 = -DNO_FRAME_POINTER
@DISABLE_RTTI_TRUE@am__append_4 = -fno-rtti
TESTS = logging_unittest$(EXEEXT) demangle_unittest$(EXEEXT) \
	stacktrace_unittest$(EXEEXT) symbolize_unittest$(EXEEXT) \
	stl_logging_unittest$(EXEEXT) utilities_unittest$(EXEEXT) \
	$(am__EXEEXT_1)
@HAVE_GMOCK_TRUE@am__append_5 = mock_log_test
noinst_PROGRAMS = $(am__EXEEXT_2) $(am__EXEEXT_3)
subdir = .
DIST_COMMON = INSTALL NEWS README AUTHORS ChangeLog \
	$(srcdir)/Makefile.in $(srcdir)/Makefile.am \
	$(top_srcdir)/configure $(am__configure_deps) \
	$(top_srcdir)/src/config.h.in mkinstalldirs \
	$(top_srcdir)/src/glog/logging.h.in \
	$(top_srcdir)/src/glog/raw_logging.h.in \
	$(top_srcdir)/src/glog/vlog_is_on.h.in \
	$(top_srcdir)/src/glog/stl_logging.h.in \
	$(srcdir)/libglog.pc.in depcomp $(dist_doc_DATA) \
	$(gloginclude_HEADERS) $(noinst_HEADERS) test-driver COPYING \
	compile config.guess config.sub install-sh missing ltmain.sh
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ac_have_attribute.m4 \
	$(top_srcdir)/m4/ac_have_builtin_expect.m4 \
	$(top_srcdir)/m4/ac_have_sync_val_compare_and_swap.m4 \
	$(top_srcdir)/m4/ac_rwlock.m4 $(top_srcdir)/m4/acx_pthread.m4 \
	$(top_srcdir)/m4/google_namespace.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 $(top_srcdir)/m4/namespaces.m4 \
	$(top_srcdir)/m4/pc_from_ucontext.m4 \
	$(top_srcdir)/m4/stl_namespace.m4 \
	$(top_srcdir)/m4/using_operator.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
am__CONFIG_DISTCLEAN_FILES = config.status config.cache config.log \
 configure.lineno config.status.lineno
mkinstalldirs = $(SHELL) $(top_srcdir)/mkinstalldirs
CONFIG_HEADER = $(top_builddir)/src/config.h
CONFIG_CLEAN_FILES = src/glog/logging.h src/glog/raw_logging.h \
	src/glog/vlog_is_on.h src/glog/stl_logging.h libglog.pc
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)" "$(DESTDIR)$(docdir)" \
	"$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(glogincludedir)" \
	"$(DESTDIR)$(glogincludedir)"
LTLIBRARIES = $(lib_LTLIBRARIES)
am__DEPENDENCIES_1 =
am__DEPENDENCIES_2 = $(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1)
libglog_la_DEPENDENCIES = $(am__DEPENDENCIES_2)
am__objects_1 =
am__dirstamp = $(am__leading_dot)dirstamp
am_libglog_la_OBJECTS = $(am__objects_1) src/libglog_la-logging.lo \
	src/libglog_la-raw_logging.lo src/libglog_la-vlog_is_on.lo \
	src/libglog_la-utilities.lo src/libglog_la-demangle.lo \
	src/libglog_la-symbolize.lo src/libglog_la-signalhandler.lo
nodist_libglog_la_OBJECTS = $(am__objects_1)
libglog_la_OBJECTS = $(am_libglog_la_OBJECTS) \
	$(nodist_libglog_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
libglog_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(libglog_la_CXXFLAGS) \
	$(CXXFLAGS) $(libglog_la_LDFLAGS) $(LDFLAGS) -o $@
@HAVE_GMOCK_TRUE@am__EXEEXT_1 = mock_log_test$(EXEEXT)
am__EXEEXT_2 = logging_unittest$(EXEEXT) demangle_unittest$(EXEEXT) \
	stacktrace_unittest$(EXEEXT) symbolize_unittest$(EXEEXT) \
	stl_logging_unittest$(EXEEXT) utilities_unittest$(EXEEXT) \
	$(am__EXEEXT_1)
am__EXEEXT_3 = logging_striptest0$(EXEEXT) logging_striptest2$(EXEEXT) \
	logging_striptest10$(EXEEXT) signalhandler_unittest$(EXEEXT)
PROGRAMS = $(noinst_PROGRAMS)
am_demangle_unittest_OBJECTS = $(am__objects_1) \
	src/demangle_unittest-demangle_unittest.$(OBJEXT)
nodist_demangle_unittest_OBJECTS = $(am__objects_1)
demangle_unittest_OBJECTS = $(am_demangle_unittest_OBJECTS) \
	$(nodist_demangle_unittest_OBJECTS)
demangle_unittest_DEPENDENCIES = libglog.la $(am__DEPENDENCIES_2) \
	$(am__DEPENDENCIES_2)
demangle_unittest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(demangle_unittest_CXXFLAGS) $(CXXFLAGS) \
	$(demangle_unittest_LDFLAGS) $(LDFLAGS) -o $@
am_logging_striptest0_OBJECTS = $(am__objects_1) \
	src/logging_striptest0-logging_striptest_main.$(OBJEXT)
nodist_logging_striptest0_OBJECTS = $(am__objects_1)
logging_striptest0_OBJECTS = $(am_logging_striptest0_OBJECTS) \
	$(nodist_logging_striptest0_OBJECTS)
logging_striptest0_DEPENDENCIES = libglog.la $(am__DEPENDENCIES_2)
logging_striptest0_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(logging_striptest0_CXXFLAGS) $(CXXFLAGS) \
	$(logging_striptest0_LDFLAGS) $(LDFLAGS) -o $@
am_logging_striptest10_OBJECTS = $(am__objects_1) \
	src/logging_striptest10-logging_striptest10.$(OBJEXT)
nodist_logging_striptest10_OBJECTS = $(am__objects_1)
logging_striptest10_OBJECTS = $(am_logging_striptest10_OBJECTS) \
	$(nodist_logging_striptest10_OBJECTS)
logging_striptest10_DEPENDENCIES = libglog.la $(am__DEPENDENCIES_2)
logging_striptest10_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(logging_striptest10_CXXFLAGS) $(CXXFLAGS) \
	$(logging_striptest10_LDFLAGS) $(LDFLAGS) -o $@
am_logging_striptest2_OBJECTS = $(am__objects_1) \
	src/logging_striptest2-logging_striptest2.$(OBJEXT)
nodist_logging_striptest2_OBJECTS = $(am__objects_1)
logging_striptest2_OBJECTS = $(am_logging_striptest2_OBJECTS) \
	$(nodist_logging_striptest2_OBJECTS)
logging_striptest2_DEPENDENCIES = libglog.la $(am__DEPENDENCIES_2)
logging_striptest2_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(logging_striptest2_CXXFLAGS) $(CXXFLAGS) \
	$(logging_striptest2_LDFLAGS) $(LDFLAGS) -o $@
am_logging_unittest_OBJECTS = $(am__objects_1) \
	src/logging_unittest-logging_unittest.$(OBJEXT)
nodist_logging_unittest_OBJECTS = $(am__objects_1)
logging_unittest_OBJECTS = $(am_logging_unittest_OBJECTS) \
	$(nodist_logging_unittest_OBJECTS)
logging_unittest_DEPENDENCIES = libglog.la $(am__DEPENDENCIES_2) \
	$(am__DEPENDENCIES_2)
logging_unittest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(logging_unittest_CXXFLAGS) $(CXXFLAGS) \
	$(logging_unittest_LDFLAGS) $(LDFLAGS) -o $@
am__mock_log_test_SOURCES_DIST = src/glog/log_severity.h \
	src/mock-log_test.cc
@HAVE_GMOCK_TRUE@am_mock_log_test_OBJECTS = $(am__objects_1) \
@HAVE_GMOCK_TRUE@	src/mock_log_test-mock-log_test.$(OBJEXT)
@HAVE_GMOCK_TRUE@nodist_mock_log_test_OBJECTS = $(am__objects_1)
mock_log_test_OBJECTS = $(am_mock_log_test_OBJECTS) \
	$(nodist_mock_log_test_OBJECTS)
@HAVE_GMOCK_TRUE@mock_log_test_DEPENDENCIES = libglog.la \
@HAVE_GMOCK_TRUE@	$(am__DEPENDENCIES_2) $(am__DEPENDENCIES_2)
mock_log_test_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(mock_log_test_CXXFLAGS) $(CXXFLAGS) $(mock_log_test_LDFLAGS) \
	$(LDFLAGS) -o $@
am_signalhandler_unittest_OBJECTS = $(am__objects_1) \
	src/signalhandler_unittest-signalhandler_unittest.$(OBJEXT)
nodist_signalhandler_unittest_OBJECTS = $(am__objects_1)
signalhandler_unittest_OBJECTS = $(am_signalhandler_unittest_OBJECTS) \
	$(nodist_signalhandler_unittest_OBJECTS)
signalhandler_unittest_DEPENDENCIES = libglog.la $(am__DEPENDENCIES_2) \
	$(am__DEPENDENCIES_2)
signalhandler_unittest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(signalhandler_unittest_CXXFLAGS) $(CXXFLAGS) \
	$(signalhandler_unittest_LDFLAGS) $(LDFLAGS) -o $@
am_stacktrace_unittest_OBJECTS = $(am__objects_1) \
	src/stacktrace_unittest-stacktrace_unittest.$(OBJEXT)
nodist_stacktrace_unittest_OBJECTS = $(am__objects_1)
stacktrace_unittest_OBJECTS = $(am_stacktrace_unittest_OBJECTS) \
	$(nodist_stacktrace_unittest_OBJECTS)
stacktrace_unittest_DEPENDENCIES = libglog.la $(am__DEPENDENCIES_2)
stacktrace_unittest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(stacktrace_unittest_CXXFLAGS) $(CXXFLAGS) \
	$(stacktrace_unittest_LDFLAGS) $(LDFLAGS) -o $@
am_stl_logging_unittest_OBJECTS = $(am__objects_1) \
	src/stl_logging_unittest-stl_logging_unittest.$(OBJEXT)
nodist_stl_logging_unittest_OBJECTS = $(am__objects_1)
stl_logging_unittest_OBJECTS = $(am_stl_logging_unittest_OBJECTS) \
	$(nodist_stl_logging_unittest_OBJECTS)
stl_logging_unittest_DEPENDENCIES = libglog.la $(am__DEPENDENCIES_2) \
	$(am__DEPENDENCIES_2)
stl_logging_unittest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(stl_logging_unittest_CXXFLAGS) $(CXXFLAGS) \
	$(stl_logging_unittest_LDFLAGS) $(LDFLAGS) -o $@
am_symbolize_unittest_OBJECTS = $(am__objects_1) \
	src/symbolize_unittest-symbolize_unittest.$(OBJEXT)
nodist_symbolize_unittest_OBJECTS = $(am__objects_1)
symbolize_unittest_OBJECTS = $(am_symbolize_unittest_OBJECTS) \
	$(nodist_symbolize_unittest_OBJECTS)
symbolize_unittest_DEPENDENCIES = libglog.la $(am__DEPENDENCIES_2) \
	$(am__DEPENDENCIES_2)
symbolize_unittest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(symbolize_unittest_CXXFLAGS) $(CXXFLAGS) \
	$(symbolize_unittest_LDFLAGS) $(LDFLAGS) -o $@
am_utilities_unittest_OBJECTS = $(am__objects_1) \
	src/utilities_unittest-utilities_unittest.$(OBJEXT)
nodist_utilities_unittest_OBJECTS = $(am__objects_1)
utilities_unittest_OBJECTS = $(am_utilities_unittest_OBJECTS) \
	$(nodist_utilities_unittest_OBJECTS)
utilities_unittest_DEPENDENCIES = libglog.la $(am__DEPENDENCIES_2) \
	$(am__DEPENDENCIES_2)
utilities_unittest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(utilities_unittest_CXXFLAGS) $(CXXFLAGS) \
	$(utilities_unittest_LDFLAGS) $(LDFLAGS) -o $@
SCRIPTS = $(noinst_SCRIPTS)
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)/src
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__depfiles_maybe = depfiles
am__mv = mv -f
CXXCOMPILE = $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS)
LTCXXCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CXXFLAGS) $(CXXFLAGS)
AM_V_CXX = $(am__v_CXX_@AM_V@)
am__v_CXX_ = $(am__v_CXX_@AM_DEFAULT_V@)
am__v_CXX_0 = @echo "  CXX     " $@;
am__v_CXX_1 = 
CXXLD = $(CXX)
CXXLINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(AM_CXXFLAGS) \
	$(CXXFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CXXLD = $(am__v_CXXLD_@AM_V@)
am__v_CXXLD_ = $(am__v_CXXLD_@AM_DEFAULT_V@)
am__v_CXXLD_0 = @echo "  CXXLD   " $@;
am__v_CXXLD_1 = 
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libglog_la_SOURCES) $(nodist_libglog_la_SOURCES) \
	$(demangle_unittest_SOURCES) \
	$(nodist_demangle_unittest_SOURCES) \
	$(logging_striptest0_SOURCES) \
	$(nodist_logging_striptest0_SOURCES) \
	$(logging_striptest10_SOURCES) \
	$(nodist_logging_striptest10_SOURCES) \
	$(logging_striptest2_SOURCES) \
	$(nodist_logging_striptest2_SOURCES) \
	$(logging_unittest_SOURCES) $(nodist_logging_unittest_SOURCES) \
	$(mock_log_test_SOURCES) $(nodist_mock_log_test_SOURCES) \
	$(signalhandler_unittest_SOURCES) \
	$(nodist_signalhandler_unittest_SOURCES) \
	$(stacktrace_unittest_SOURCES) \
	$(nodist_stacktrace_unittest_SOURCES) \
	$(stl_logging_unittest_SOURCES) \
	$(nodist_stl_logging_unittest_SOURCES) \
	$(symbolize_unittest_SOURCES) \
	$(nodist_symbolize_unittest_SOURCES) \
	$(utilities_unittest_SOURCES) \
	$(nodist_utilities_unittest_SOURCES)
DIST_SOURCES = $(libglog_la_SOURCES) $(demangle_unittest_SOURCES) \
	$(logging_striptest0_SOURCES) $(logging_striptest10_SOURCES) \
	$(logging_striptest2_SOURCES) $(logging_unittest_SOURCES) \
	$(am__mock_log_test_SOURCES_DIST) \
	$(signalhandler_unittest_SOURCES) \
	$(stacktrace_unittest_SOURCES) $(stl_logging_unittest_SOURCES) \
	$(symbolize_unittest_SOURCES) $(utilities_unittest_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
DATA = $(dist_doc_DATA) $(pkgconfig_DATA)
HEADERS = $(gloginclude_HEADERS) $(nodist_gloginclude_HEADERS) \
	$(noinst_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
CSCOPE = cscope
AM_RECURSIVE_TARGETS = cscope check recheck
am__tty_colors_dummy = \
  mgn= red= grn= lgn= blu= brg= std=; \
  am__color_tests=no
am__tty_colors = { \
  $(am__tty_colors_dummy); \
  if test "X$(AM_COLOR_TESTS)" = Xno; then \
    am__color_tests=no; \
  elif test "X$(AM_COLOR_TESTS)" = Xalways; then \
    am__color_tests=yes; \
  elif test "X$$TERM" != Xdumb && { test -t 1; } 2>/dev/null; then \
    am__color_tests=yes; \
  fi; \
  if test $$am__color_tests = yes; then \
    red='[0;31m'; \
    grn='[0;32m'; \
    lgn='[1;32m'; \
    blu='[1;34m'; \
    mgn='[0;35m'; \
    brg='[1m'; \
    std='[m'; \
  fi; \
}
am__recheck_rx = ^[ 	]*:recheck:[ 	]*
am__global_test_result_rx = ^[ 	]*:global-test-result:[ 	]*
am__copy_in_global_log_rx = ^[ 	]*:copy-in-global-log:[ 	]*
# A command that, given a newline-separated list of test names on the
# standard input, print the name of the tests that are to be re-run
# upon "make recheck".
am__list_recheck_tests = $(AWK) '{ \
  recheck = 1; \
  while ((rc = (getline line < ($$0 ".trs"))) != 0) \
    { \
      if (rc < 0) \
        { \
          if ((getline line2 < ($$0 ".log")) < 0) \
	    recheck = 0; \
          break; \
        } \
      else if (line ~ /$(am__recheck_rx)[nN][Oo]/) \
        { \
          recheck = 0; \
          break; \
        } \
      else if (line ~ /$(am__recheck_rx)[yY][eE][sS]/) \
        { \
          break; \
        } \
    }; \
  if (recheck) \
    print $$0; \
  close ($$0 ".trs"); \
  close ($$0 ".log"); \
}'
# A command that, given a newline-separated list of test names on the
# standard input, create the global log from their .trs and .log files.
am__create_global_log = $(AWK) ' \
function fatal(msg) \
{ \
  print "fatal: making $@: " msg | "cat >&2"; \
  exit 1; \
} \
function rst_section(header) \
{ \
  print header; \
  len = length(header); \
  for (i = 1; i <= len; i = i + 1) \
    printf "="; \
  printf "\n\n"; \
} \
{ \
  copy_in_global_log = 1; \
  global_test_result = "RUN"; \
  while ((rc = (getline line < ($$0 ".trs"))) != 0) \
    { \
      if (rc < 0) \
         fatal("failed to read from " $$0 ".trs"); \
      if (line ~ /$(am__global_test_result_rx)/) \
        { \
          sub("$(am__global_test_result_rx)", "", line); \
          sub("[ 	]*$$", "", line); \
          global_test_result = line; \
        } \
      else if (line ~ /$(am__copy_in_global_log_rx)[nN][oO]/) \
        copy_in_global_log = 0; \
    }; \
  if (copy_in_global_log) \
    { \
      rst_section(global_test_result ": " $$0); \
      while ((rc = (getline line < ($$0 ".log"))) != 0) \
      { \
        if (rc < 0) \
          fatal("failed to read from " $$0 ".log"); \
        print line; \
      }; \
      printf "\n"; \
    }; \
  close ($$0 ".trs"); \
  close ($$0 ".log"); \
}'
# Restructured Text title.
am__rst_title = { sed 's/.*/   &   /;h;s/./=/g;p;x;s/ *$$//;p;g' && echo; }
# Solaris 10 'make', and several other traditional 'make' implementations,
# pass "-e" to $(SHELL), and POSIX 2008 even requires this.  Work around it
# by disabling -e (using the XSI extension "set +e") if it's set.
am__sh_e_setup = case $$- in *e*) set +e;; esac
# Default flags passed to test drivers.
am__common_driver_flags = \
  --color-tests "$$am__color_tests" \
  --enable-hard-errors "$$am__enable_hard_errors" \
  --expect-failure "$$am__expect_failure"
# To be inserted before the command running the test.  Creates the
# directory for the log if needed.  Stores in $dir the directory
# containing $f, in $tst the test, in $log the log.  Executes the
# developer- defined test setup AM_TESTS_ENVIRONMENT (if any), and
# passes TESTS_ENVIRONMENT.  Set up options for the wrapper that
# will run the test scripts (or their associated LOG_COMPILER, if
# thy have one).
am__check_pre = \
$(am__sh_e_setup);					\
$(am__vpath_adj_setup) $(am__vpath_adj)			\
$(am__tty_colors);					\
srcdir=$(srcdir); export srcdir;			\
case "$@" in						\
  */*) am__odir=`echo "./$@" | sed 's|/[^/]*$$||'`;;	\
    *) am__odir=.;; 					\
esac;							\
test "x$$am__odir" = x"." || test -d "$$am__odir" 	\
  || $(MKDIR_P) "$$am__odir" || exit $$?;		\
if test -f "./$$f"; then dir=./;			\
elif test -f "$$f"; then dir=;				\
else dir="$(srcdir)/"; fi;				\
tst=$$dir$$f; log='$@'; 				\
if test -n '$(DISABLE_HARD_ERRORS)'; then		\
  am__enable_hard_errors=no; 				\
else							\
  am__enable_hard_errors=yes; 				\
fi; 							\
case " $(XFAIL_TESTS) " in				\
  *[\ \	]$$f[\ \	]* | *[\ \	]$$dir$$f[\ \	]*) \
    am__expect_failure=yes;;				\
  *)							\
    am__expect_failure=no;;				\
esac; 							\
$(AM_TESTS_ENVIRONMENT) $(TESTS_ENVIRONMENT)
# A shell command to get the names of the tests scripts with any registered
# extension removed (i.e., equivalently, the names of the test logs, with
# the '.log' extension removed).  The result is saved in the shell variable
# '$bases'.  This honors runtime overriding of TESTS and TEST_LOGS.  Sadly,
# we cannot use something simpler, involving e.g., "$(TEST_LOGS:.log=)",
# since that might cause problem with VPATH rewrites for suffix-less tests.
# See also 'test-harness-vpath-rewrite.sh' and 'test-trs-basic.sh'.
am__set_TESTS_bases = \
  bases='$(TEST_LOGS)'; \
  bases=`for i in $$bases; do echo $$i; done | sed 's/\.log$$//'`; \
  bases=`echo $$bases`
RECHECK_LOGS = $(TEST_LOGS)
TEST_SUITE_LOG = test-suite.log
TEST_EXTENSIONS = @EXEEXT@ .test
LOG_DRIVER = $(SHELL) $(top_srcdir)/test-driver
LOG_COMPILE = $(LOG_COMPILER) $(AM_LOG_FLAGS) $(LOG_FLAGS)
am__set_b = \
  case '$@' in \
    */*) \
      case '$*' in \
        */*) b='$*';; \
          *) b=`echo '$@' | sed 's/\.log$$//'`; \
       esac;; \
    *) \
      b='$*';; \
  esac
am__test_logs1 = $(TESTS:=.log)
am__test_logs2 = $(am__test_logs1:@EXEEXT@.log=.log)
TEST_LOGS = $(am__test_logs2:.test.log=.log)
TEST_LOG_DRIVER = $(SHELL) $(top_srcdir)/test-driver
TEST_LOG_COMPILE = $(TEST_LOG_COMPILER) $(AM_TEST_LOG_FLAGS) \
	$(TEST_LOG_FLAGS)
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
distdir = $(PACKAGE)-$(VERSION)
top_distdir = $(distdir)
am__remove_distdir = \
  if test -d "$(distdir)"; then \
    find "$(distdir)" -type d ! -perm -200 -exec chmod u+w {} ';' \
      && rm -rf "$(distdir)" \
      || { sleep 5 && rm -rf "$(distdir)"; }; \
  else :; fi
am__post_remove_distdir = $(am__remove_distdir)
DIST_ARCHIVES = $(distdir).tar.gz
GZIP_ENV = --best
DIST_TARGETS = dist-gzip
distuninstallcheck_listfiles = find . -type f -print
am__distuninstallcheck_listfiles = $(distuninstallcheck_listfiles) \
  | sed 's|^\./|$(prefix)/|' | grep -v '$(infodir)/dir$$'
distcleancheck_listfiles = find . -type f -print
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GFLAGS_CFLAGS = @GFLAGS_CFLAGS@
GFLAGS_LIBS = @GFLAGS_LIBS@
GMOCK_CFLAGS = @GMOCK_CFLAGS@
GMOCK_CONFIG = @GMOCK_CONFIG@
GMOCK_LIBS = @GMOCK_LIBS@
GREP = @GREP@
GTEST_CFLAGS = @GTEST_CFLAGS@
GTEST_CONFIG = @GTEST_CONFIG@
GTEST_LIBS = @GTEST_LIBS@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIBTOOL_DEPS = @LIBTOOL_DEPS@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MINGW_CFLAGS = @MINGW_CFLAGS@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PTHREAD_CC = @PTHREAD_CC@
PTHREAD_CFLAGS = @PTHREAD_CFLAGS@
PTHREAD_LIBS = @PTHREAD_LIBS@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
UNWIND_LIBS = @UNWIND_LIBS@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
ac_cv___attribute___noinline = @ac_cv___attribute___noinline@
ac_cv___attribute___noreturn = @ac_cv___attribute___noreturn@
ac_cv___attribute___printf_4_5 = @ac_cv___attribute___printf_4_5@
ac_cv_cxx_using_operator = @ac_cv_cxx_using_operator@
ac_cv_have___builtin_expect = @ac_cv_have___builtin_expect@
ac_cv_have___uint16 = @ac_cv_have___uint16@
ac_cv_have_inttypes_h = @ac_cv_have_inttypes_h@
ac_cv_have_libgflags = @ac_cv_have_libgflags@
ac_cv_have_stdint_h = @ac_cv_have_stdint_h@
ac_cv_have_systypes_h = @ac_cv_have_systypes_h@
ac_cv_have_u_int16_t = @ac_cv_have_u_int16_t@
ac_cv_have_uint16_t = @ac_cv_have_uint16_t@
ac_cv_have_unistd_h = @ac_cv_have_unistd_h@
ac_google_end_namespace = @ac_google_end_namespace@
ac_google_namespace = @ac_google_namespace@
ac_google_start_namespace = @ac_google_start_namespace@
acx_pthread_config = @acx_pthread_config@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = $(prefix)/share/doc/$(PACKAGE)-$(VERSION)
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
AUTOMAKE_OPTIONS = subdir-objects

# Make sure that when we re-make ./configure, we get the macros we need
ACLOCAL_AMFLAGS = -I m4

# This is so we can #include <glog/foo>
AM_CPPFLAGS = -I$(top_srcdir)/src

# This is mostly based on configure options
AM_CXXFLAGS = $(am__append_1) $(am__append_2) $(am__append_3) \
	$(am__append_4)
glogincludedir = $(includedir)/glog
gloginclude_HEADERS = src/glog/log_severity.h
nodist_gloginclude_HEADERS = src/glog/logging.h src/glog/raw_logging.h src/glog/vlog_is_on.h src/glog/stl_logging.h
noinst_HEADERS = src/glog/logging.h.in src/glog/raw_logging.h.in src/glog/vlog_is_on.h.in src/glog/stl_logging.h.in
dist_doc_DATA = AUTHORS COPYING ChangeLog INSTALL NEWS README README.windows \
                doc/designstyle.css doc/glog.html

lib_LTLIBRARIES = libglog.la

# The libraries libglog depends on.
COMMON_LIBS = $(PTHREAD_LIBS) $(GFLAGS_LIBS) $(UNWIND_LIBS)
# Compile switches for our unittest.
TEST_CFLAGS = $(GTEST_CFLAGS) $(GMOCK_CFLAGS) $(GFLAGS_CFLAGS) \
              $(MINGW_CFLAGS) $(AM_CXXFLAGS)

# Libraries for our unittest.
TEST_LIBS = $(GTEST_LIBS) $(GMOCK_LIBS) $(GFLAGS_LIBS)
TESTS_ENVIRONMENT = 
check_SCRIPTS = logging_striplog_test_sh demangle_unittest_sh \
	signalhandler_unittest_sh
# Every time you add a unittest to check_SCRIPTS, add it here too
noinst_SCRIPTS = src/logging_striplog_test.sh src/demangle_unittest.sh \
	src/signalhandler_unittest.sh
# Binaries used for script-based unittests.
TEST_BINARIES = logging_striptest0 logging_striptest2 \
	logging_striptest10 signalhandler_unittest
logging_unittest_SOURCES = $(gloginclude_HEADERS) \
                           src/logging_unittest.cc \
                           src/config_for_unittests.h \
                           src/mock-log.h

nodist_logging_unittest_SOURCES = $(nodist_gloginclude_HEADERS)
logging_unittest_CXXFLAGS = $(PTHREAD_CFLAGS) $(TEST_CFLAGS)
logging_unittest_LDFLAGS = $(PTHREAD_CFLAGS)
logging_unittest_LDADD = libglog.la $(COMMON_LIBS) $(TEST_LIBS)
logging_striptest0_SOURCES = $(gloginclude_HEADERS) \
                             src/logging_striptest_main.cc

nodist_logging_striptest0_SOURCES = $(nodist_gloginclude_HEADERS)
logging_striptest0_CXXFLAGS = $(PTHREAD_CFLAGS) $(TEST_CFLAGS)
logging_striptest0_LDFLAGS = $(PTHREAD_CFLAGS)
logging_striptest0_LDADD = libglog.la $(COMMON_LIBS)
logging_striptest2_SOURCES = $(gloginclude_HEADERS) \
                             src/logging_striptest2.cc

nodist_logging_striptest2_SOURCES = $(nodist_gloginclude_HEADERS)
logging_striptest2_CXXFLAGS = $(PTHREAD_CFLAGS) $(TEST_CFLAGS)
logging_striptest2_LDFLAGS = $(PTHREAD_CFLAGS)
logging_striptest2_LDADD = libglog.la $(COMMON_LIBS)
logging_striptest10_SOURCES = $(gloginclude_HEADERS) \
                              src/logging_striptest10.cc

nodist_logging_striptest10_SOURCES = $(nodist_gloginclude_HEADERS)
logging_striptest10_CXXFLAGS = $(PTHREAD_CFLAGS) $(TEST_CFLAGS)
logging_striptest10_LDFLAGS = $(PTHREAD_CFLAGS)
logging_striptest10_LDADD = libglog.la $(COMMON_LIBS)
demangle_unittest_SOURCES = $(gloginclude_HEADERS) \
                            src/demangle_unittest.cc

nodist_demangle_unittest_SOURCES = $(nodist_gloginclude_HEADERS)
demangle_unittest_CXXFLAGS = $(PTHREAD_CFLAGS) $(TEST_CFLAGS)
demangle_unittest_LDFLAGS = $(PTHREAD_CFLAGS)
demangle_unittest_LDADD = libglog.la $(COMMON_LIBS) $(TEST_LIBS)
stacktrace_unittest_SOURCES = $(gloginclude_HEADERS) \
                              src/stacktrace_unittest.cc

nodist_stacktrace_unittest_SOURCES = $(nodist_gloginclude_HEADERS)
stacktrace_unittest_CXXFLAGS = $(PTHREAD_CFLAGS) $(TEST_CFLAGS)
stacktrace_unittest_LDFLAGS = $(PTHREAD_CFLAGS)
stacktrace_unittest_LDADD = libglog.la $(COMMON_LIBS)
symbolize_unittest_SOURCES = $(gloginclude_HEADERS) \
                              src/symbolize_unittest.cc

nodist_symbolize_unittest_SOURCES = $(nodist_gloginclude_HEADERS)
symbolize_unittest_CXXFLAGS = $(PTHREAD_CFLAGS) $(TEST_CFLAGS)
symbolize_unittest_LDFLAGS = $(PTHREAD_CFLAGS)
symbolize_unittest_LDADD = libglog.la $(COMMON_LIBS) $(TEST_LIBS)
stl_logging_unittest_SOURCES = $(gloginclude_HEADERS) \
                               src/stl_logging_unittest.cc

nodist_stl_logging_unittest_SOURCES = $(nodist_gloginclude_HEADERS)
stl_logging_unittest_CXXFLAGS = $(PTHREAD_CFLAGS) $(TEST_CFLAGS)
stl_logging_unittest_LDFLAGS = $(PTHREAD_CFLAGS)
stl_logging_unittest_LDADD = libglog.la $(COMMON_LIBS) $(TEST_LIBS)
signalhandler_unittest_SOURCES = $(gloginclude_HEADERS) \
                               src/signalhandler_unittest.cc

nodist_signalhandler_unittest_SOURCES = $(nodist_gloginclude_HEADERS)
signalhandler_unittest_CXXFLAGS = $(PTHREAD_CFLAGS) $(TEST_CFLAGS)
signalhandler_unittest_LDFLAGS = $(PTHREAD_CFLAGS)
signalhandler_unittest_LDADD = libglog.la $(COMMON_LIBS) $(TEST_LIBS)
utilities_unittest_SOURCES = $(gloginclude_HEADERS) \
                             src/utilities_unittest.cc

nodist_utilities_unittest_SOURCES = $(nodist_gloginclude_HEADERS)
utilities_unittest_CXXFLAGS = $(PTHREAD_CFLAGS) $(TEST_CFLAGS)
utilities_unittest_LDFLAGS = $(PTHREAD_CFLAGS)
utilities_unittest_LDADD = libglog.la $(COMMON_LIBS) $(TEST_LIBS)
@HAVE_GMOCK_TRUE@mock_log_test_SOURCES = $(gloginclude_HEADERS) \
@HAVE_GMOCK_TRUE@                        src/mock-log_test.cc

@HAVE_GMOCK_TRUE@nodist_mock_log_test_SOURCES = $(nodist_gloginclude_HEADERS)
@HAVE_GMOCK_TRUE@mock_log_test_CXXFLAGS = $(PTHREAD_CFLAGS) $(TEST_CFLAGS)
@HAVE_GMOCK_TRUE@mock_log_test_LDFLAGS = $(PTHREAD_CFLAGS)
@HAVE_GMOCK_TRUE@mock_log_test_LDADD = libglog.la $(COMMON_LIBS) $(TEST_LIBS)
libglog_la_SOURCES = $(gloginclude_HEADERS) \
                       src/logging.cc src/raw_logging.cc src/vlog_is_on.cc \
                       src/utilities.cc src/utilities.h \
                       src/demangle.cc src/demangle.h \
                       src/stacktrace.h \
                       src/stacktrace_generic-inl.h \
                       src/stacktrace_libunwind-inl.h \
                       src/stacktrace_powerpc-inl.h \
                       src/stacktrace_x86-inl.h \
                       src/stacktrace_x86_64-inl.h \
                       src/symbolize.cc src/symbolize.h \
                       src/signalhandler.cc \
                       src/base/mutex.h src/base/googleinit.h \
                       src/base/commandlineflags.h src/googletest.h

nodist_libglog_la_SOURCES = $(nodist_gloginclude_HEADERS)
libglog_la_CXXFLAGS = $(PTRHEAD_CFLAGS) $(GFLAGS_CFLAGS) $(MINGW_CFLAGS) \
                      $(AM_CXXFLAGS) -DNDEBUG

libglog_la_LDFLAGS = $(PTRHEAD_CFLAGS) $(GFLAGS_LDFLAGS)
libglog_la_LIBADD = $(COMMON_LIBS)
WINDOWS_PROJECTS = google-glog.sln vsprojects/libglog/libglog.vcproj \
	vsprojects/logging_unittest/logging_unittest.vcproj \
	vsprojects/libglog_static/libglog_static.vcproj \
	vsprojects/logging_unittest_static/logging_unittest_static.vcproj
EXTRA_DIST = packages/rpm.sh packages/rpm/rpm.spec \
	packages/deb.sh packages/deb/* \
	$(SCRIPTS) src/logging_unittest.err src/demangle_unittest.txt \
	src/windows/config.h src/windows/port.h src/windows/port.cc \
	src/windows/preprocess.sh \
	src/windows/glog/log_severity.h src/windows/glog/logging.h \
	src/windows/glog/raw_logging.h src/windows/glog/stl_logging.h \
	src/windows/glog/vlog_is_on.h \
	$(WINDOWS_PROJECTS)

CLEANFILES = core demangle.dm demangle.nm signalhandler.out* \
	signalhandler_unittest.*.log.INFO.*


# Add pkgconfig file
pkgconfigdir = $(libdir)/pkgconfig
pkgconfig_DATA = libglog.pc
all: all-am

.SUFFIXES:
.SUFFIXES: .cc .lo .log .o .obj .test .test$(EXEEXT) .trs
am--refresh: Makefile
	@:
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      echo ' cd $(srcdir) && $(AUTOMAKE) --gnu'; \
	      $(am__cd) $(srcdir) && $(AUTOMAKE) --gnu \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    echo ' $(SHELL) ./config.status'; \
	    $(SHELL) ./config.status;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	$(SHELL) ./config.status --recheck

$(top_srcdir)/configure:  $(am__configure_deps)
	$(am__cd) $(srcdir) && $(AUTOCONF)
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	$(am__cd) $(srcdir) && $(ACLOCAL) $(ACLOCAL_AMFLAGS)
$(am__aclocal_m4_deps):

src/config.h: src/stamp-h1
	@test -f $@ || rm -f src/stamp-h1
	@test -f $@ || $(MAKE) $(AM_MAKEFLAGS) src/stamp-h1

src/stamp-h1: $(top_srcdir)/src/config.h.in $(top_builddir)/config.status
	@rm -f src/stamp-h1
	cd $(top_builddir) && $(SHELL) ./config.status src/config.h
$(top_srcdir)/src/config.h.in:  $(am__configure_deps) 
	($(am__cd) $(top_srcdir) && $(AUTOHEADER))
	rm -f src/stamp-h1
	touch $@

distclean-hdr:
	-rm -f src/config.h src/stamp-h1
src/glog/logging.h: $(top_builddir)/config.status $(top_srcdir)/src/glog/logging.h.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
src/glog/raw_logging.h: $(top_builddir)/config.status $(top_srcdir)/src/glog/raw_logging.h.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
src/glog/vlog_is_on.h: $(top_builddir)/config.status $(top_srcdir)/src/glog/vlog_is_on.h.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
src/glog/stl_logging.h: $(top_builddir)/config.status $(top_srcdir)/src/glog/stl_logging.h.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
libglog.pc: $(top_builddir)/config.status $(srcdir)/libglog.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
src/$(am__dirstamp):
	@$(MKDIR_P) src
	@: > src/$(am__dirstamp)
src/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/$(DEPDIR)
	@: > src/$(DEPDIR)/$(am__dirstamp)
src/libglog_la-logging.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libglog_la-raw_logging.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libglog_la-vlog_is_on.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libglog_la-utilities.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libglog_la-demangle.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libglog_la-symbolize.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libglog_la-signalhandler.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)

libglog.la: $(libglog_la_OBJECTS) $(libglog_la_DEPENDENCIES) $(EXTRA_libglog_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(libglog_la_LINK) -rpath $(libdir) $(libglog_la_OBJECTS) $(libglog_la_LIBADD) $(LIBS)

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list
src/demangle_unittest-demangle_unittest.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)

demangle_unittest$(EXEEXT): $(demangle_unittest_OBJECTS) $(demangle_unittest_DEPENDENCIES) $(EXTRA_demangle_unittest_DEPENDENCIES) 
	@rm -f demangle_unittest$(EXEEXT)
	$(AM_V_CXXLD)$(demangle_unittest_LINK) $(demangle_unittest_OBJECTS) $(demangle_unittest_LDADD) $(LIBS)
src/logging_striptest0-logging_striptest_main.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)

logging_striptest0$(EXEEXT): $(logging_striptest0_OBJECTS) $(logging_striptest0_DEPENDENCIES) $(EXTRA_logging_striptest0_DEPENDENCIES) 
	@rm -f logging_striptest0$(EXEEXT)
	$(AM_V_CXXLD)$(logging_striptest0_LINK) $(logging_striptest0_OBJECTS) $(logging_striptest0_LDADD) $(LIBS)
src/logging_striptest10-logging_striptest10.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)

logging_striptest10$(EXEEXT): $(logging_striptest10_OBJECTS) $(logging_striptest10_DEPENDENCIES) $(EXTRA_logging_striptest10_DEPENDENCIES) 
	@rm -f logging_striptest10$(EXEEXT)
	$(AM_V_CXXLD)$(logging_striptest10_LINK) $(logging_striptest10_OBJECTS) $(logging_striptest10_LDADD) $(LIBS)
src/logging_striptest2-logging_striptest2.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)

logging_striptest2$(EXEEXT): $(logging_striptest2_OBJECTS) $(logging_striptest2_DEPENDENCIES) $(EXTRA_logging_striptest2_DEPENDENCIES) 
	@rm -f logging_striptest2$(EXEEXT)
	$(AM_V_CXXLD)$(logging_striptest2_LINK) $(logging_striptest2_OBJECTS) $(logging_striptest2_LDADD) $(LIBS)
src/logging_unittest-logging_unittest.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)

logging_unittest$(EXEEXT): $(logging_unittest_OBJECTS) $(logging_unittest_DEPENDENCIES) $(EXTRA_logging_unittest_DEPENDENCIES) 
	@rm -f logging_unittest$(EXEEXT)
	$(AM_V_CXXLD)$(logging_unittest_LINK) $(logging_unittest_OBJECTS) $(logging_unittest_LDADD) $(LIBS)
src/mock_log_test-mock-log_test.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)

mock_log_test$(EXEEXT): $(mock_log_test_OBJECTS) $(mock_log_test_DEPENDENCIES) $(EXTRA_mock_log_test_DEPENDENCIES) 
	@rm -f mock_log_test$(EXEEXT)
	$(AM_V_CXXLD)$(mock_log_test_LINK) $(mock_log_test_OBJECTS) $(mock_log_test_LDADD) $(LIBS)
src/signalhandler_unittest-signalhandler_unittest.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)

signalhandler_unittest$(EXEEXT): $(signalhandler_unittest_OBJECTS) $(signalhandler_unittest_DEPENDENCIES) $(EXTRA_signalhandler_unittest_DEPENDENCIES) 
	@rm -f signalhandler_unittest$(EXEEXT)
	$(AM_V_CXXLD)$(signalhandler_unittest_LINK) $(signalhandler_unittest_OBJECTS) $(signalhandler_unittest_LDADD) $(LIBS)
src/stacktrace_unittest-stacktrace_unittest.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)

stacktrace_unittest$(EXEEXT): $(stacktrace_unittest_OBJECTS) $(stacktrace_unittest_DEPENDENCIES) $(EXTRA_stacktrace_unittest_DEPENDENCIES) 
	@rm -f stacktrace_unittest$(EXEEXT)
	$(AM_V_CXXLD)$(stacktrace_unittest_LINK) $(stacktrace_unittest_OBJECTS) $(stacktrace_unittest_LDADD) $(LIBS)
src/stl_logging_unittest-stl_logging_unittest.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)

stl_logging_unittest$(EXEEXT): $(stl_logging_unittest_OBJECTS) $(stl_logging_unittest_DEPENDENCIES) $(EXTRA_stl_logging_unittest_DEPENDENCIES) 
	@rm -f stl_logging_unittest$(EXEEXT)
	$(AM_V_CXXLD)$(stl_logging_unittest_LINK) $(stl_logging_unittest_OBJECTS) $(stl_logging_unittest_LDADD) $(LIBS)
src/symbolize_unittest-symbolize_unittest.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)

symbolize_unittest$(EXEEXT): $(symbolize_unittest_OBJECTS) $(symbolize_unittest_DEPENDENCIES) $(EXTRA_symbolize_unittest_DEPENDENCIES) 
	@rm -f symbolize_unittest$(EXEEXT)
	$(AM_V_CXXLD)$(symbolize_unittest_LINK) $(symbolize_unittest_OBJECTS) $(symbolize_unittest_LDADD) $(LIBS)
src/utilities_unittest-utilities_unittest.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)

utilities_unittest$(EXEEXT): $(utilities_unittest_OBJECTS) $(utilities_unittest_DEPENDENCIES) $(EXTRA_utilities_unittest_DEPENDENCIES) 
	@rm -f utilities_unittest$(EXEEXT)
	$(AM_V_CXXLD)$(utilities_unittest_LINK) $(utilities_unittest_OBJECTS) $(utilities_unittest_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f src/*.$(OBJEXT)
	-rm -f src/*.lo

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/demangle_unittest-demangle_unittest.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libglog_la-demangle.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libglog_la-logging.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libglog_la-raw_logging.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libglog_la-signalhandler.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libglog_la-symbolize.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libglog_la-utilities.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libglog_la-vlog_is_on.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/logging_striptest0-logging_striptest_main.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/logging_striptest10-logging_striptest10.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/logging_striptest2-logging_striptest2.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/logging_unittest-logging_unittest.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/mock_log_test-mock-log_test.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/signalhandler_unittest-signalhandler_unittest.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/stacktrace_unittest-stacktrace_unittest.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/stl_logging_unittest-stl_logging_unittest.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/symbolize_unittest-symbolize_unittest.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/utilities_unittest-utilities_unittest.Po@am__quote@

.cc.o:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCXX_TRUE@	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ $<

.cc.obj:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCXX_TRUE@	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.cc.lo:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCXX_TRUE@	$(LTCXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LTCXXCOMPILE) -c -o $@ $<

src/libglog_la-logging.lo: src/logging.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -MT src/libglog_la-logging.lo -MD -MP -MF src/$(DEPDIR)/libglog_la-logging.Tpo -c -o src/libglog_la-logging.lo `test -f 'src/logging.cc' || echo '$(srcdir)/'`src/logging.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libglog_la-logging.Tpo src/$(DEPDIR)/libglog_la-logging.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/logging.cc' object='src/libglog_la-logging.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -c -o src/libglog_la-logging.lo `test -f 'src/logging.cc' || echo '$(srcdir)/'`src/logging.cc

src/libglog_la-raw_logging.lo: src/raw_logging.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -MT src/libglog_la-raw_logging.lo -MD -MP -MF src/$(DEPDIR)/libglog_la-raw_logging.Tpo -c -o src/libglog_la-raw_logging.lo `test -f 'src/raw_logging.cc' || echo '$(srcdir)/'`src/raw_logging.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libglog_la-raw_logging.Tpo src/$(DEPDIR)/libglog_la-raw_logging.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/raw_logging.cc' object='src/libglog_la-raw_logging.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -c -o src/libglog_la-raw_logging.lo `test -f 'src/raw_logging.cc' || echo '$(srcdir)/'`src/raw_logging.cc

src/libglog_la-vlog_is_on.lo: src/vlog_is_on.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -MT src/libglog_la-vlog_is_on.lo -MD -MP -MF src/$(DEPDIR)/libglog_la-vlog_is_on.Tpo -c -o src/libglog_la-vlog_is_on.lo `test -f 'src/vlog_is_on.cc' || echo '$(srcdir)/'`src/vlog_is_on.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libglog_la-vlog_is_on.Tpo src/$(DEPDIR)/libglog_la-vlog_is_on.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/vlog_is_on.cc' object='src/libglog_la-vlog_is_on.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -c -o src/libglog_la-vlog_is_on.lo `test -f 'src/vlog_is_on.cc' || echo '$(srcdir)/'`src/vlog_is_on.cc

src/libglog_la-utilities.lo: src/utilities.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -MT src/libglog_la-utilities.lo -MD -MP -MF src/$(DEPDIR)/libglog_la-utilities.Tpo -c -o src/libglog_la-utilities.lo `test -f 'src/utilities.cc' || echo '$(srcdir)/'`src/utilities.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libglog_la-utilities.Tpo src/$(DEPDIR)/libglog_la-utilities.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/utilities.cc' object='src/libglog_la-utilities.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -c -o src/libglog_la-utilities.lo `test -f 'src/utilities.cc' || echo '$(srcdir)/'`src/utilities.cc

src/libglog_la-demangle.lo: src/demangle.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -MT src/libglog_la-demangle.lo -MD -MP -MF src/$(DEPDIR)/libglog_la-demangle.Tpo -c -o src/libglog_la-demangle.lo `test -f 'src/demangle.cc' || echo '$(srcdir)/'`src/demangle.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libglog_la-demangle.Tpo src/$(DEPDIR)/libglog_la-demangle.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/demangle.cc' object='src/libglog_la-demangle.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -c -o src/libglog_la-demangle.lo `test -f 'src/demangle.cc' || echo '$(srcdir)/'`src/demangle.cc

src/libglog_la-symbolize.lo: src/symbolize.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -MT src/libglog_la-symbolize.lo -MD -MP -MF src/$(DEPDIR)/libglog_la-symbolize.Tpo -c -o src/libglog_la-symbolize.lo `test -f 'src/symbolize.cc' || echo '$(srcdir)/'`src/symbolize.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libglog_la-symbolize.Tpo src/$(DEPDIR)/libglog_la-symbolize.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/symbolize.cc' object='src/libglog_la-symbolize.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -c -o src/libglog_la-symbolize.lo `test -f 'src/symbolize.cc' || echo '$(srcdir)/'`src/symbolize.cc

src/libglog_la-signalhandler.lo: src/signalhandler.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -MT src/libglog_la-signalhandler.lo -MD -MP -MF src/$(DEPDIR)/libglog_la-signalhandler.Tpo -c -o src/libglog_la-signalhandler.lo `test -f 'src/signalhandler.cc' || echo '$(srcdir)/'`src/signalhandler.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libglog_la-signalhandler.Tpo src/$(DEPDIR)/libglog_la-signalhandler.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/signalhandler.cc' object='src/libglog_la-signalhandler.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libglog_la_CXXFLAGS) $(CXXFLAGS) -c -o src/libglog_la-signalhandler.lo `test -f 'src/signalhandler.cc' || echo '$(srcdir)/'`src/signalhandler.cc

src/demangle_unittest-demangle_unittest.o: src/demangle_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(demangle_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/demangle_unittest-demangle_unittest.o -MD -MP -MF src/$(DEPDIR)/demangle_unittest-demangle_unittest.Tpo -c -o src/demangle_unittest-demangle_unittest.o `test -f 'src/demangle_unittest.cc' || echo '$(srcdir)/'`src/demangle_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/demangle_unittest-demangle_unittest.Tpo src/$(DEPDIR)/demangle_unittest-demangle_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/demangle_unittest.cc' object='src/demangle_unittest-demangle_unittest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(demangle_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/demangle_unittest-demangle_unittest.o `test -f 'src/demangle_unittest.cc' || echo '$(srcdir)/'`src/demangle_unittest.cc

src/demangle_unittest-demangle_unittest.obj: src/demangle_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(demangle_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/demangle_unittest-demangle_unittest.obj -MD -MP -MF src/$(DEPDIR)/demangle_unittest-demangle_unittest.Tpo -c -o src/demangle_unittest-demangle_unittest.obj `if test -f 'src/demangle_unittest.cc'; then $(CYGPATH_W) 'src/demangle_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/demangle_unittest.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/demangle_unittest-demangle_unittest.Tpo src/$(DEPDIR)/demangle_unittest-demangle_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/demangle_unittest.cc' object='src/demangle_unittest-demangle_unittest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(demangle_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/demangle_unittest-demangle_unittest.obj `if test -f 'src/demangle_unittest.cc'; then $(CYGPATH_W) 'src/demangle_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/demangle_unittest.cc'; fi`

src/logging_striptest0-logging_striptest_main.o: src/logging_striptest_main.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest0_CXXFLAGS) $(CXXFLAGS) -MT src/logging_striptest0-logging_striptest_main.o -MD -MP -MF src/$(DEPDIR)/logging_striptest0-logging_striptest_main.Tpo -c -o src/logging_striptest0-logging_striptest_main.o `test -f 'src/logging_striptest_main.cc' || echo '$(srcdir)/'`src/logging_striptest_main.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/logging_striptest0-logging_striptest_main.Tpo src/$(DEPDIR)/logging_striptest0-logging_striptest_main.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/logging_striptest_main.cc' object='src/logging_striptest0-logging_striptest_main.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest0_CXXFLAGS) $(CXXFLAGS) -c -o src/logging_striptest0-logging_striptest_main.o `test -f 'src/logging_striptest_main.cc' || echo '$(srcdir)/'`src/logging_striptest_main.cc

src/logging_striptest0-logging_striptest_main.obj: src/logging_striptest_main.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest0_CXXFLAGS) $(CXXFLAGS) -MT src/logging_striptest0-logging_striptest_main.obj -MD -MP -MF src/$(DEPDIR)/logging_striptest0-logging_striptest_main.Tpo -c -o src/logging_striptest0-logging_striptest_main.obj `if test -f 'src/logging_striptest_main.cc'; then $(CYGPATH_W) 'src/logging_striptest_main.cc'; else $(CYGPATH_W) '$(srcdir)/src/logging_striptest_main.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/logging_striptest0-logging_striptest_main.Tpo src/$(DEPDIR)/logging_striptest0-logging_striptest_main.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/logging_striptest_main.cc' object='src/logging_striptest0-logging_striptest_main.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest0_CXXFLAGS) $(CXXFLAGS) -c -o src/logging_striptest0-logging_striptest_main.obj `if test -f 'src/logging_striptest_main.cc'; then $(CYGPATH_W) 'src/logging_striptest_main.cc'; else $(CYGPATH_W) '$(srcdir)/src/logging_striptest_main.cc'; fi`

src/logging_striptest10-logging_striptest10.o: src/logging_striptest10.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest10_CXXFLAGS) $(CXXFLAGS) -MT src/logging_striptest10-logging_striptest10.o -MD -MP -MF src/$(DEPDIR)/logging_striptest10-logging_striptest10.Tpo -c -o src/logging_striptest10-logging_striptest10.o `test -f 'src/logging_striptest10.cc' || echo '$(srcdir)/'`src/logging_striptest10.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/logging_striptest10-logging_striptest10.Tpo src/$(DEPDIR)/logging_striptest10-logging_striptest10.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/logging_striptest10.cc' object='src/logging_striptest10-logging_striptest10.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest10_CXXFLAGS) $(CXXFLAGS) -c -o src/logging_striptest10-logging_striptest10.o `test -f 'src/logging_striptest10.cc' || echo '$(srcdir)/'`src/logging_striptest10.cc

src/logging_striptest10-logging_striptest10.obj: src/logging_striptest10.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest10_CXXFLAGS) $(CXXFLAGS) -MT src/logging_striptest10-logging_striptest10.obj -MD -MP -MF src/$(DEPDIR)/logging_striptest10-logging_striptest10.Tpo -c -o src/logging_striptest10-logging_striptest10.obj `if test -f 'src/logging_striptest10.cc'; then $(CYGPATH_W) 'src/logging_striptest10.cc'; else $(CYGPATH_W) '$(srcdir)/src/logging_striptest10.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/logging_striptest10-logging_striptest10.Tpo src/$(DEPDIR)/logging_striptest10-logging_striptest10.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/logging_striptest10.cc' object='src/logging_striptest10-logging_striptest10.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest10_CXXFLAGS) $(CXXFLAGS) -c -o src/logging_striptest10-logging_striptest10.obj `if test -f 'src/logging_striptest10.cc'; then $(CYGPATH_W) 'src/logging_striptest10.cc'; else $(CYGPATH_W) '$(srcdir)/src/logging_striptest10.cc'; fi`

src/logging_striptest2-logging_striptest2.o: src/logging_striptest2.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest2_CXXFLAGS) $(CXXFLAGS) -MT src/logging_striptest2-logging_striptest2.o -MD -MP -MF src/$(DEPDIR)/logging_striptest2-logging_striptest2.Tpo -c -o src/logging_striptest2-logging_striptest2.o `test -f 'src/logging_striptest2.cc' || echo '$(srcdir)/'`src/logging_striptest2.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/logging_striptest2-logging_striptest2.Tpo src/$(DEPDIR)/logging_striptest2-logging_striptest2.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/logging_striptest2.cc' object='src/logging_striptest2-logging_striptest2.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest2_CXXFLAGS) $(CXXFLAGS) -c -o src/logging_striptest2-logging_striptest2.o `test -f 'src/logging_striptest2.cc' || echo '$(srcdir)/'`src/logging_striptest2.cc

src/logging_striptest2-logging_striptest2.obj: src/logging_striptest2.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest2_CXXFLAGS) $(CXXFLAGS) -MT src/logging_striptest2-logging_striptest2.obj -MD -MP -MF src/$(DEPDIR)/logging_striptest2-logging_striptest2.Tpo -c -o src/logging_striptest2-logging_striptest2.obj `if test -f 'src/logging_striptest2.cc'; then $(CYGPATH_W) 'src/logging_striptest2.cc'; else $(CYGPATH_W) '$(srcdir)/src/logging_striptest2.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/logging_striptest2-logging_striptest2.Tpo src/$(DEPDIR)/logging_striptest2-logging_striptest2.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/logging_striptest2.cc' object='src/logging_striptest2-logging_striptest2.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_striptest2_CXXFLAGS) $(CXXFLAGS) -c -o src/logging_striptest2-logging_striptest2.obj `if test -f 'src/logging_striptest2.cc'; then $(CYGPATH_W) 'src/logging_striptest2.cc'; else $(CYGPATH_W) '$(srcdir)/src/logging_striptest2.cc'; fi`

src/logging_unittest-logging_unittest.o: src/logging_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/logging_unittest-logging_unittest.o -MD -MP -MF src/$(DEPDIR)/logging_unittest-logging_unittest.Tpo -c -o src/logging_unittest-logging_unittest.o `test -f 'src/logging_unittest.cc' || echo '$(srcdir)/'`src/logging_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/logging_unittest-logging_unittest.Tpo src/$(DEPDIR)/logging_unittest-logging_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/logging_unittest.cc' object='src/logging_unittest-logging_unittest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/logging_unittest-logging_unittest.o `test -f 'src/logging_unittest.cc' || echo '$(srcdir)/'`src/logging_unittest.cc

src/logging_unittest-logging_unittest.obj: src/logging_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/logging_unittest-logging_unittest.obj -MD -MP -MF src/$(DEPDIR)/logging_unittest-logging_unittest.Tpo -c -o src/logging_unittest-logging_unittest.obj `if test -f 'src/logging_unittest.cc'; then $(CYGPATH_W) 'src/logging_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/logging_unittest.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/logging_unittest-logging_unittest.Tpo src/$(DEPDIR)/logging_unittest-logging_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/logging_unittest.cc' object='src/logging_unittest-logging_unittest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(logging_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/logging_unittest-logging_unittest.obj `if test -f 'src/logging_unittest.cc'; then $(CYGPATH_W) 'src/logging_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/logging_unittest.cc'; fi`

src/mock_log_test-mock-log_test.o: src/mock-log_test.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(mock_log_test_CXXFLAGS) $(CXXFLAGS) -MT src/mock_log_test-mock-log_test.o -MD -MP -MF src/$(DEPDIR)/mock_log_test-mock-log_test.Tpo -c -o src/mock_log_test-mock-log_test.o `test -f 'src/mock-log_test.cc' || echo '$(srcdir)/'`src/mock-log_test.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/mock_log_test-mock-log_test.Tpo src/$(DEPDIR)/mock_log_test-mock-log_test.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/mock-log_test.cc' object='src/mock_log_test-mock-log_test.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(mock_log_test_CXXFLAGS) $(CXXFLAGS) -c -o src/mock_log_test-mock-log_test.o `test -f 'src/mock-log_test.cc' || echo '$(srcdir)/'`src/mock-log_test.cc

src/mock_log_test-mock-log_test.obj: src/mock-log_test.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(mock_log_test_CXXFLAGS) $(CXXFLAGS) -MT src/mock_log_test-mock-log_test.obj -MD -MP -MF src/$(DEPDIR)/mock_log_test-mock-log_test.Tpo -c -o src/mock_log_test-mock-log_test.obj `if test -f 'src/mock-log_test.cc'; then $(CYGPATH_W) 'src/mock-log_test.cc'; else $(CYGPATH_W) '$(srcdir)/src/mock-log_test.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/mock_log_test-mock-log_test.Tpo src/$(DEPDIR)/mock_log_test-mock-log_test.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/mock-log_test.cc' object='src/mock_log_test-mock-log_test.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(mock_log_test_CXXFLAGS) $(CXXFLAGS) -c -o src/mock_log_test-mock-log_test.obj `if test -f 'src/mock-log_test.cc'; then $(CYGPATH_W) 'src/mock-log_test.cc'; else $(CYGPATH_W) '$(srcdir)/src/mock-log_test.cc'; fi`

src/signalhandler_unittest-signalhandler_unittest.o: src/signalhandler_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(signalhandler_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/signalhandler_unittest-signalhandler_unittest.o -MD -MP -MF src/$(DEPDIR)/signalhandler_unittest-signalhandler_unittest.Tpo -c -o src/signalhandler_unittest-signalhandler_unittest.o `test -f 'src/signalhandler_unittest.cc' || echo '$(srcdir)/'`src/signalhandler_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/signalhandler_unittest-signalhandler_unittest.Tpo src/$(DEPDIR)/signalhandler_unittest-signalhandler_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/signalhandler_unittest.cc' object='src/signalhandler_unittest-signalhandler_unittest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(signalhandler_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/signalhandler_unittest-signalhandler_unittest.o `test -f 'src/signalhandler_unittest.cc' || echo '$(srcdir)/'`src/signalhandler_unittest.cc

src/signalhandler_unittest-signalhandler_unittest.obj: src/signalhandler_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(signalhandler_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/signalhandler_unittest-signalhandler_unittest.obj -MD -MP -MF src/$(DEPDIR)/signalhandler_unittest-signalhandler_unittest.Tpo -c -o src/signalhandler_unittest-signalhandler_unittest.obj `if test -f 'src/signalhandler_unittest.cc'; then $(CYGPATH_W) 'src/signalhandler_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/signalhandler_unittest.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/signalhandler_unittest-signalhandler_unittest.Tpo src/$(DEPDIR)/signalhandler_unittest-signalhandler_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/signalhandler_unittest.cc' object='src/signalhandler_unittest-signalhandler_unittest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(signalhandler_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/signalhandler_unittest-signalhandler_unittest.obj `if test -f 'src/signalhandler_unittest.cc'; then $(CYGPATH_W) 'src/signalhandler_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/signalhandler_unittest.cc'; fi`

src/stacktrace_unittest-stacktrace_unittest.o: src/stacktrace_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(stacktrace_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/stacktrace_unittest-stacktrace_unittest.o -MD -MP -MF src/$(DEPDIR)/stacktrace_unittest-stacktrace_unittest.Tpo -c -o src/stacktrace_unittest-stacktrace_unittest.o `test -f 'src/stacktrace_unittest.cc' || echo '$(srcdir)/'`src/stacktrace_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/stacktrace_unittest-stacktrace_unittest.Tpo src/$(DEPDIR)/stacktrace_unittest-stacktrace_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/stacktrace_unittest.cc' object='src/stacktrace_unittest-stacktrace_unittest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(stacktrace_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/stacktrace_unittest-stacktrace_unittest.o `test -f 'src/stacktrace_unittest.cc' || echo '$(srcdir)/'`src/stacktrace_unittest.cc

src/stacktrace_unittest-stacktrace_unittest.obj: src/stacktrace_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(stacktrace_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/stacktrace_unittest-stacktrace_unittest.obj -MD -MP -MF src/$(DEPDIR)/stacktrace_unittest-stacktrace_unittest.Tpo -c -o src/stacktrace_unittest-stacktrace_unittest.obj `if test -f 'src/stacktrace_unittest.cc'; then $(CYGPATH_W) 'src/stacktrace_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/stacktrace_unittest.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/stacktrace_unittest-stacktrace_unittest.Tpo src/$(DEPDIR)/stacktrace_unittest-stacktrace_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/stacktrace_unittest.cc' object='src/stacktrace_unittest-stacktrace_unittest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(stacktrace_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/stacktrace_unittest-stacktrace_unittest.obj `if test -f 'src/stacktrace_unittest.cc'; then $(CYGPATH_W) 'src/stacktrace_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/stacktrace_unittest.cc'; fi`

src/stl_logging_unittest-stl_logging_unittest.o: src/stl_logging_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(stl_logging_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/stl_logging_unittest-stl_logging_unittest.o -MD -MP -MF src/$(DEPDIR)/stl_logging_unittest-stl_logging_unittest.Tpo -c -o src/stl_logging_unittest-stl_logging_unittest.o `test -f 'src/stl_logging_unittest.cc' || echo '$(srcdir)/'`src/stl_logging_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/stl_logging_unittest-stl_logging_unittest.Tpo src/$(DEPDIR)/stl_logging_unittest-stl_logging_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/stl_logging_unittest.cc' object='src/stl_logging_unittest-stl_logging_unittest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(stl_logging_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/stl_logging_unittest-stl_logging_unittest.o `test -f 'src/stl_logging_unittest.cc' || echo '$(srcdir)/'`src/stl_logging_unittest.cc

src/stl_logging_unittest-stl_logging_unittest.obj: src/stl_logging_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(stl_logging_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/stl_logging_unittest-stl_logging_unittest.obj -MD -MP -MF src/$(DEPDIR)/stl_logging_unittest-stl_logging_unittest.Tpo -c -o src/stl_logging_unittest-stl_logging_unittest.obj `if test -f 'src/stl_logging_unittest.cc'; then $(CYGPATH_W) 'src/stl_logging_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/stl_logging_unittest.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/stl_logging_unittest-stl_logging_unittest.Tpo src/$(DEPDIR)/stl_logging_unittest-stl_logging_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/stl_logging_unittest.cc' object='src/stl_logging_unittest-stl_logging_unittest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(stl_logging_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/stl_logging_unittest-stl_logging_unittest.obj `if test -f 'src/stl_logging_unittest.cc'; then $(CYGPATH_W) 'src/stl_logging_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/stl_logging_unittest.cc'; fi`

src/symbolize_unittest-symbolize_unittest.o: src/symbolize_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(symbolize_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/symbolize_unittest-symbolize_unittest.o -MD -MP -MF src/$(DEPDIR)/symbolize_unittest-symbolize_unittest.Tpo -c -o src/symbolize_unittest-symbolize_unittest.o `test -f 'src/symbolize_unittest.cc' || echo '$(srcdir)/'`src/symbolize_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/symbolize_unittest-symbolize_unittest.Tpo src/$(DEPDIR)/symbolize_unittest-symbolize_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/symbolize_unittest.cc' object='src/symbolize_unittest-symbolize_unittest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(symbolize_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/symbolize_unittest-symbolize_unittest.o `test -f 'src/symbolize_unittest.cc' || echo '$(srcdir)/'`src/symbolize_unittest.cc

src/symbolize_unittest-symbolize_unittest.obj: src/symbolize_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(symbolize_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/symbolize_unittest-symbolize_unittest.obj -MD -MP -MF src/$(DEPDIR)/symbolize_unittest-symbolize_unittest.Tpo -c -o src/symbolize_unittest-symbolize_unittest.obj `if test -f 'src/symbolize_unittest.cc'; then $(CYGPATH_W) 'src/symbolize_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/symbolize_unittest.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/symbolize_unittest-symbolize_unittest.Tpo src/$(DEPDIR)/symbolize_unittest-symbolize_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/symbolize_unittest.cc' object='src/symbolize_unittest-symbolize_unittest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(symbolize_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/symbolize_unittest-symbolize_unittest.obj `if test -f 'src/symbolize_unittest.cc'; then $(CYGPATH_W) 'src/symbolize_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/symbolize_unittest.cc'; fi`

src/utilities_unittest-utilities_unittest.o: src/utilities_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(utilities_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/utilities_unittest-utilities_unittest.o -MD -MP -MF src/$(DEPDIR)/utilities_unittest-utilities_unittest.Tpo -c -o src/utilities_unittest-utilities_unittest.o `test -f 'src/utilities_unittest.cc' || echo '$(srcdir)/'`src/utilities_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/utilities_unittest-utilities_unittest.Tpo src/$(DEPDIR)/utilities_unittest-utilities_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/utilities_unittest.cc' object='src/utilities_unittest-utilities_unittest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(utilities_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/utilities_unittest-utilities_unittest.o `test -f 'src/utilities_unittest.cc' || echo '$(srcdir)/'`src/utilities_unittest.cc

src/utilities_unittest-utilities_unittest.obj: src/utilities_unittest.cc
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(utilities_unittest_CXXFLAGS) $(CXXFLAGS) -MT src/utilities_unittest-utilities_unittest.obj -MD -MP -MF src/$(DEPDIR)/utilities_unittest-utilities_unittest.Tpo -c -o src/utilities_unittest-utilities_unittest.obj `if test -f 'src/utilities_unittest.cc'; then $(CYGPATH_W) 'src/utilities_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/utilities_unittest.cc'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/utilities_unittest-utilities_unittest.Tpo src/$(DEPDIR)/utilities_unittest-utilities_unittest.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='src/utilities_unittest.cc' object='src/utilities_unittest-utilities_unittest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(utilities_unittest_CXXFLAGS) $(CXXFLAGS) -c -o src/utilities_unittest-utilities_unittest.obj `if test -f 'src/utilities_unittest.cc'; then $(CYGPATH_W) 'src/utilities_unittest.cc'; else $(CYGPATH_W) '$(srcdir)/src/utilities_unittest.cc'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf src/.libs src/_libs

distclean-libtool:
	-rm -f libtool config.lt
install-dist_docDATA: $(dist_doc_DATA)
	@$(NORMAL_INSTALL)
	@list='$(dist_doc_DATA)'; test -n "$(docdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(docdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(docdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(docdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(docdir)" || exit $$?; \
	done

uninstall-dist_docDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(dist_doc_DATA)'; test -n "$(docdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(docdir)'; $(am__uninstall_files_from_dir)
install-pkgconfigDATA: $(pkgconfig_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgconfigdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgconfigdir)" || exit $$?; \
	done

uninstall-pkgconfigDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgconfigdir)'; $(am__uninstall_files_from_dir)
install-glogincludeHEADERS: $(gloginclude_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(gloginclude_HEADERS)'; test -n "$(glogincludedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(glogincludedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(glogincludedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(glogincludedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(glogincludedir)" || exit $$?; \
	done

uninstall-glogincludeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(gloginclude_HEADERS)'; test -n "$(glogincludedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(glogincludedir)'; $(am__uninstall_files_from_dir)
install-nodist_glogincludeHEADERS: $(nodist_gloginclude_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(nodist_gloginclude_HEADERS)'; test -n "$(glogincludedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(glogincludedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(glogincludedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(glogincludedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(glogincludedir)" || exit $$?; \
	done

uninstall-nodist_glogincludeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(nodist_gloginclude_HEADERS)'; test -n "$(glogincludedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(glogincludedir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscope: cscope.files
	test ! -s cscope.files \
	  || $(CSCOPE) -b -q $(AM_CSCOPEFLAGS) $(CSCOPEFLAGS) -i cscope.files $(CSCOPE_ARGS)
clean-cscope:
	-rm -f cscope.files
cscope.files: clean-cscope cscopelist
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
	-rm -f cscope.out cscope.in.out cscope.po.out cscope.files

# Recover from deleted '.trs' file; this should ensure that
# "rm -f foo.log; make foo.trs" re-run 'foo.test', and re-create
# both 'foo.log' and 'foo.trs'.  Break the recipe in two subshells
# to avoid problems with "make -n".
.log.trs:
	rm -f $< $@
	$(MAKE) $(AM_MAKEFLAGS) $<

# Leading 'am--fnord' is there to ensure the list of targets does not
# expand to empty, as could happen e.g. with make check TESTS=''.
am--fnord $(TEST_LOGS) $(TEST_LOGS:.log=.trs): $(am__force_recheck)
am--force-recheck:
	@:

$(TEST_SUITE_LOG): $(TEST_LOGS)
	@$(am__set_TESTS_bases); \
	am__f_ok () { test -f "$$1" && test -r "$$1"; }; \
	redo_bases=`for i in $$bases; do \
	              am__f_ok $$i.trs && am__f_ok $$i.log || echo $$i; \
	            done`; \
	if test -n "$$redo_bases"; then \
	  redo_logs=`for i in $$redo_bases; do echo $$i.log; done`; \
	  redo_results=`for i in $$redo_bases; do echo $$i.trs; done`; \
	  if $(am__make_dryrun); then :; else \
	    rm -f $$redo_logs && rm -f $$redo_results || exit 1; \
	  fi; \
	fi; \
	if test -n "$$am__remaking_logs"; then \
	  echo "fatal: making $(TEST_SUITE_LOG): possible infinite" \
	       "recursion detected" >&2; \
	else \
	  am__remaking_logs=yes $(MAKE) $(AM_MAKEFLAGS) $$redo_logs; \
	fi; \
	if $(am__make_dryrun); then :; else \
	  st=0;  \
	  errmsg="fatal: making $(TEST_SUITE_LOG): failed to create"; \
	  for i in $$redo_bases; do \
	    test -f $$i.trs && test -r $$i.trs \
	      || { echo "$$errmsg $$i.trs" >&2; st=1; }; \
	    test -f $$i.log && test -r $$i.log \
	      || { echo "$$errmsg $$i.log" >&2; st=1; }; \
	  done; \
	  test $$st -eq 0 || exit 1; \
	fi
	@$(am__sh_e_setup); $(am__tty_colors); $(am__set_TESTS_bases); \
	ws='[ 	]'; \
	results=`for b in $$bases; do echo $$b.trs; done`; \
	test -n "$$results" || results=/dev/null; \
	all=`  grep "^$$ws*:test-result:"           $$results | wc -l`; \
	pass=` grep "^$$ws*:test-result:$$ws*PASS"  $$results | wc -l`; \
	fail=` grep "^$$ws*:test-result:$$ws*FAIL"  $$results | wc -l`; \
	skip=` grep "^$$ws*:test-result:$$ws*SKIP"  $$results | wc -l`; \
	xfail=`grep "^$$ws*:test-result:$$ws*XFAIL" $$results | wc -l`; \
	xpass=`grep "^$$ws*:test-result:$$ws*XPASS" $$results | wc -l`; \
	error=`grep "^$$ws*:test-result:$$ws*ERROR" $$results | wc -l`; \
	if test `expr $$fail + $$xpass + $$error` -eq 0; then \
	  success=true; \
	else \
	  success=false; \
	fi; \
	br='==================='; br=$$br$$br$$br$$br; \
	result_count () \
	{ \
	    if test x"$$1" = x"--maybe-color"; then \
	      maybe_colorize=yes; \
	    elif test x"$$1" = x"--no-color"; then \
	      maybe_colorize=no; \
	    else \
	      echo "$@: invalid 'result_count' usage" >&2; exit 4; \
	    fi; \
	    shift; \
	    desc=$$1 count=$$2; \
	    if test $$maybe_colorize = yes && test $$count -gt 0; then \
	      color_start=$$3 color_end=$$std; \
	    else \
	      color_start= color_end=; \
	    fi; \
	    echo "$${color_start}# $$desc $$count$${color_end}"; \
	}; \
	create_testsuite_report () \
	{ \
	  result_count $$1 "TOTAL:" $$all   "$$brg"; \
	  result_count $$1 "PASS: " $$pass  "$$grn"; \
	  result_count $$1 "SKIP: " $$skip  "$$blu"; \
	  result_count $$1 "XFAIL:" $$xfail "$$lgn"; \
	  result_count $$1 "FAIL: " $$fail  "$$red"; \
	  result_count $$1 "XPASS:" $$xpass "$$red"; \
	  result_count $$1 "ERROR:" $$error "$$mgn"; \
	}; \
	{								\
	  echo "$(PACKAGE_STRING): $(subdir)/$(TEST_SUITE_LOG)" |	\
	    $(am__rst_title);						\
	  create_testsuite_report --no-color;				\
	  echo;								\
	  echo ".. contents:: :depth: 2";				\
	  echo;								\
	  for b in $$bases; do echo $$b; done				\
	    | $(am__create_global_log);					\
	} >$(TEST_SUITE_LOG).tmp || exit 1;				\
	mv $(TEST_SUITE_LOG).tmp $(TEST_SUITE_LOG);			\
	if $$success; then						\
	  col="$$grn";							\
	 else								\
	  col="$$red";							\
	  test x"$$VERBOSE" = x || cat $(TEST_SUITE_LOG);		\
	fi;								\
	echo "$${col}$$br$${std}"; 					\
	echo "$${col}Testsuite summary for $(PACKAGE_STRING)$${std}";	\
	echo "$${col}$$br$${std}"; 					\
	create_testsuite_report --maybe-color;				\
	echo "$$col$$br$$std";						\
	if $$success; then :; else					\
	  echo "$${col}See $(subdir)/$(TEST_SUITE_LOG)$${std}";		\
	  if test -n "$(PACKAGE_BUGREPORT)"; then			\
	    echo "$${col}Please report to $(PACKAGE_BUGREPORT)$${std}";	\
	  fi;								\
	  echo "$$col$$br$$std";					\
	fi;								\
	$$success || exit 1

check-TESTS:
	@list='$(RECHECK_LOGS)';           test -z "$$list" || rm -f $$list
	@list='$(RECHECK_LOGS:.log=.trs)'; test -z "$$list" || rm -f $$list
	@test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)
	@set +e; $(am__set_TESTS_bases); \
	log_list=`for i in $$bases; do echo $$i.log; done`; \
	trs_list=`for i in $$bases; do echo $$i.trs; done`; \
	log_list=`echo $$log_list`; trs_list=`echo $$trs_list`; \
	$(MAKE) $(AM_MAKEFLAGS) $(TEST_SUITE_LOG) TEST_LOGS="$$log_list"; \
	exit $$?;
recheck: all $(check_SCRIPTS)
	@test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)
	@set +e; $(am__set_TESTS_bases); \
	bases=`for i in $$bases; do echo $$i; done \
	         | $(am__list_recheck_tests)` || exit 1; \
	log_list=`for i in $$bases; do echo $$i.log; done`; \
	log_list=`echo $$log_list`; \
	$(MAKE) $(AM_MAKEFLAGS) $(TEST_SUITE_LOG) \
	        am__force_recheck=am--force-recheck \
	        TEST_LOGS="$$log_list"; \
	exit $$?
logging_unittest.log: logging_unittest$(EXEEXT)
	@p='logging_unittest$(EXEEXT)'; \
	b='logging_unittest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
demangle_unittest.log: demangle_unittest$(EXEEXT)
	@p='demangle_unittest$(EXEEXT)'; \
	b='demangle_unittest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
stacktrace_unittest.log: stacktrace_unittest$(EXEEXT)
	@p='stacktrace_unittest$(EXEEXT)'; \
	b='stacktrace_unittest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
symbolize_unittest.log: symbolize_unittest$(EXEEXT)
	@p='symbolize_unittest$(EXEEXT)'; \
	b='symbolize_unittest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
stl_logging_unittest.log: stl_logging_unittest$(EXEEXT)
	@p='stl_logging_unittest$(EXEEXT)'; \
	b='stl_logging_unittest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
utilities_unittest.log: utilities_unittest$(EXEEXT)
	@p='utilities_unittest$(EXEEXT)'; \
	b='utilities_unittest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
mock_log_test.log: mock_log_test$(EXEEXT)
	@p='mock_log_test$(EXEEXT)'; \
	b='mock_log_test'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
.test.log:
	@p='$<'; \
	$(am__set_b); \
	$(am__check_pre) $(TEST_LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_TEST_LOG_DRIVER_FLAGS) $(TEST_LOG_DRIVER_FLAGS) -- $(TEST_LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
@am__EXEEXT_TRUE@.test$(EXEEXT).log:
@am__EXEEXT_TRUE@	@p='$<'; \
@am__EXEEXT_TRUE@	$(am__set_b); \
@am__EXEEXT_TRUE@	$(am__check_pre) $(TEST_LOG_DRIVER) --test-name "$$f" \
@am__EXEEXT_TRUE@	--log-file $$b.log --trs-file $$b.trs \
@am__EXEEXT_TRUE@	$(am__common_driver_flags) $(AM_TEST_LOG_DRIVER_FLAGS) $(TEST_LOG_DRIVER_FLAGS) -- $(TEST_LOG_COMPILE) \
@am__EXEEXT_TRUE@	"$$tst" $(AM_TESTS_FD_REDIRECT)

distdir: $(DISTFILES)
	$(am__remove_distdir)
	test -d "$(distdir)" || mkdir "$(distdir)"
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	$(MAKE) $(AM_MAKEFLAGS) \
	  top_distdir="$(top_distdir)" distdir="$(distdir)" \
	  dist-hook
	-test -n "$(am__skip_mode_fix)" \
	|| find "$(distdir)" -type d ! -perm -755 \
		-exec chmod u+rwx,go+rx {} \; -o \
	  ! -type d ! -perm -444 -links 1 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -400 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -444 -exec $(install_sh) -c -m a+r {} {} \; \
	|| chmod -R a+r "$(distdir)"
dist-gzip: distdir
	tardir=$(distdir) && $(am__tar) | GZIP=$(GZIP_ENV) gzip -c >$(distdir).tar.gz
	$(am__post_remove_distdir)

dist-bzip2: distdir
	tardir=$(distdir) && $(am__tar) | BZIP2=$${BZIP2--9} bzip2 -c >$(distdir).tar.bz2
	$(am__post_remove_distdir)

dist-lzip: distdir
	tardir=$(distdir) && $(am__tar) | lzip -c $${LZIP_OPT--9} >$(distdir).tar.lz
	$(am__post_remove_distdir)

dist-xz: distdir
	tardir=$(distdir) && $(am__tar) | XZ_OPT=$${XZ_OPT--e} xz -c >$(distdir).tar.xz
	$(am__post_remove_distdir)

dist-tarZ: distdir
	@echo WARNING: "Support for shar distribution archives is" \
	               "deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	tardir=$(distdir) && $(am__tar) | compress -c >$(distdir).tar.Z
	$(am__post_remove_distdir)

dist-shar: distdir
	@echo WARNING: "Support for distribution archives compressed with" \
		       "legacy program 'compress' is deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	shar $(distdir) | GZIP=$(GZIP_ENV) gzip -c >$(distdir).shar.gz
	$(am__post_remove_distdir)

dist-zip: distdir
	-rm -f $(distdir).zip
	zip -rq $(distdir).zip $(distdir)
	$(am__post_remove_distdir)

dist dist-all:
	$(MAKE) $(AM_MAKEFLAGS) $(DIST_TARGETS) am__post_remove_distdir='@:'
	$(am__post_remove_distdir)

# This target untars the dist file and tries a VPATH configuration.  Then
# it guarantees that the distribution is self-contained by making another
# tarfile.
distcheck: dist
	case '$(DIST_ARCHIVES)' in \
	*.tar.gz*) \
	  GZIP=$(GZIP_ENV) gzip -dc $(distdir).tar.gz | $(am__untar) ;;\
	*.tar.bz2*) \
	  bzip2 -dc $(distdir).tar.bz2 | $(am__untar) ;;\
	*.tar.lz*) \
	  lzip -dc $(distdir).tar.lz | $(am__untar) ;;\
	*.tar.xz*) \
	  xz -dc $(distdir).tar.xz | $(am__untar) ;;\
	*.tar.Z*) \
	  uncompress -c $(distdir).tar.Z | $(am__untar) ;;\
	*.shar.gz*) \
	  GZIP=$(GZIP_ENV) gzip -dc $(distdir).shar.gz | unshar ;;\
	*.zip*) \
	  unzip $(distdir).zip ;;\
	esac
	chmod -R a-w $(distdir)
	chmod u+w $(distdir)
	mkdir $(distdir)/_build $(distdir)/_inst
	chmod a-w $(distdir)
	test -d $(distdir)/_build || exit 0; \
	dc_install_base=`$(am__cd) $(distdir)/_inst && pwd | sed -e 's,^[^:\\/]:[\\/],/,'` \
	  && dc_destdir="$${TMPDIR-/tmp}/am-dc-$$$$/" \
	  && am__cwd=`pwd` \
	  && $(am__cd) $(distdir)/_build \
	  && ../configure \
	    $(AM_DISTCHECK_CONFIGURE_FLAGS) \
	    $(DISTCHECK_CONFIGURE_FLAGS) \
	    --srcdir=.. --prefix="$$dc_install_base" \
	  && $(MAKE) $(AM_MAKEFLAGS) \
	  && $(MAKE) $(AM_MAKEFLAGS) dvi \
	  && $(MAKE) $(AM_MAKEFLAGS) check \
	  && $(MAKE) $(AM_MAKEFLAGS) install \
	  && $(MAKE) $(AM_MAKEFLAGS) installcheck \
	  && $(MAKE) $(AM_MAKEFLAGS) uninstall \
	  && $(MAKE) $(AM_MAKEFLAGS) distuninstallcheck_dir="$$dc_install_base" \
	        distuninstallcheck \
	  && chmod -R a-w "$$dc_install_base" \
	  && ({ \
	       (cd ../.. && umask 077 && mkdir "$$dc_destdir") \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" install \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" uninstall \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" \
	            distuninstallcheck_dir="$$dc_destdir" distuninstallcheck; \
	      } || { rm -rf "$$dc_destdir"; exit 1; }) \
	  && rm -rf "$$dc_destdir" \
	  && $(MAKE) $(AM_MAKEFLAGS) dist \
	  && rm -rf $(DIST_ARCHIVES) \
	  && $(MAKE) $(AM_MAKEFLAGS) distcleancheck \
	  && cd "$$am__cwd" \
	  || exit 1
	$(am__post_remove_distdir)
	@(echo "$(distdir) archives ready for distribution: "; \
	  list='$(DIST_ARCHIVES)'; for i in $$list; do echo $$i; done) | \
	  sed -e 1h -e 1s/./=/g -e 1p -e 1x -e '$$p' -e '$$x'
distuninstallcheck:
	@test -n '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: trying to run $@ with an empty' \
	       '$$(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	$(am__cd) '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: cannot chdir into $(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	test `$(am__distuninstallcheck_listfiles) | wc -l` -eq 0 \
	   || { echo "ERROR: files left after uninstall:" ; \
	        if test -n "$(DESTDIR)"; then \
	          echo "  (check DESTDIR support)"; \
	        fi ; \
	        $(distuninstallcheck_listfiles) ; \
	        exit 1; } >&2
distcleancheck: distclean
	@if test '$(srcdir)' = . ; then \
	  echo "ERROR: distcleancheck can only run from a VPATH build" ; \
	  exit 1 ; \
	fi
	@test `$(distcleancheck_listfiles) | wc -l` -eq 0 \
	  || { echo "ERROR: files left in build directory after distclean:" ; \
	       $(distcleancheck_listfiles) ; \
	       exit 1; } >&2
check-am: all-am
	$(MAKE) $(AM_MAKEFLAGS) $(check_SCRIPTS)
	$(MAKE) $(AM_MAKEFLAGS) check-TESTS
check: check-am
all-am: Makefile $(LTLIBRARIES) $(PROGRAMS) $(SCRIPTS) $(DATA) \
		$(HEADERS)
installdirs:
	for dir in "$(DESTDIR)$(libdir)" "$(DESTDIR)$(docdir)" "$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(glogincludedir)" "$(DESTDIR)$(glogincludedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:
	-test -z "$(TEST_LOGS)" || rm -f $(TEST_LOGS)
	-test -z "$(TEST_LOGS:.log=.trs)" || rm -f $(TEST_LOGS:.log=.trs)
	-test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f src/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libLTLIBRARIES clean-libtool \
	clean-noinstPROGRAMS mostlyclean-am

distclean: distclean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf src/$(DEPDIR)
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-hdr distclean-libtool distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-dist_docDATA install-glogincludeHEADERS \
	install-nodist_glogincludeHEADERS install-pkgconfigDATA

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-libLTLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf $(top_srcdir)/autom4te.cache
	-rm -rf src/$(DEPDIR)
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-dist_docDATA uninstall-glogincludeHEADERS \
	uninstall-libLTLIBRARIES uninstall-nodist_glogincludeHEADERS \
	uninstall-pkgconfigDATA

.MAKE: check-am install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--refresh check check-TESTS \
	check-am clean clean-cscope clean-generic clean-libLTLIBRARIES \
	clean-libtool clean-noinstPROGRAMS cscope cscopelist-am ctags \
	ctags-am dist dist-all dist-bzip2 dist-gzip dist-hook \
	dist-lzip dist-shar dist-tarZ dist-xz dist-zip distcheck \
	distclean distclean-compile distclean-generic distclean-hdr \
	distclean-libtool distclean-tags distcleancheck distdir \
	distuninstallcheck dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am \
	install-dist_docDATA install-dvi install-dvi-am install-exec \
	install-exec-am install-glogincludeHEADERS install-html \
	install-html-am install-info install-info-am \
	install-libLTLIBRARIES install-man \
	install-nodist_glogincludeHEADERS install-pdf install-pdf-am \
	install-pkgconfigDATA install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	recheck tags tags-am uninstall uninstall-am \
	uninstall-dist_docDATA uninstall-glogincludeHEADERS \
	uninstall-libLTLIBRARIES uninstall-nodist_glogincludeHEADERS \
	uninstall-pkgconfigDATA

@ENABLE_FRAME_POINTERS_FALSE@@X86_64_TRUE@  # TODO(csilvers): check if -fomit-frame-pointer might be in $(CXXFLAGS),
@ENABLE_FRAME_POINTERS_FALSE@@X86_64_TRUE@  #                 before setting this.
logging_striplog_test_sh: logging_striptest0 logging_striptest2 logging_striptest10
	$(top_srcdir)/src/logging_striplog_test.sh
demangle_unittest_sh: demangle_unittest
	$(builddir)/demangle_unittest  # force to create lt-demangle_unittest
	$(top_srcdir)/src/demangle_unittest.sh
signalhandler_unittest_sh: signalhandler_unittest
	$(builddir)/signalhandler_unittest  # force to create lt-signalhandler_unittest
	$(top_srcdir)/src/signalhandler_unittest.sh

rpm: dist-gzip packages/rpm.sh packages/rpm/rpm.spec
	@cd packages && ./rpm.sh ${PACKAGE} ${VERSION}

deb: dist-gzip packages/deb.sh packages/deb/*
	@cd packages && ./deb.sh ${PACKAGE} ${VERSION}

# Windows wants write permission to .vcproj files and maybe even sln files.
dist-hook:
	test -e "$(distdir)/vsprojects" \
	   && chmod -R u+w $(distdir)/*.sln $(distdir)/vsprojects/

libtool: $(LIBTOOL_DEPS)
	$(SHELL) ./config.status --recheck

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
